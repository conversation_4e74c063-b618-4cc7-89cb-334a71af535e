import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'logger_service.dart';

enum NetworkStatus { online, offline }

class ConnectivityService {
  // Create our public controller
  StreamController<NetworkStatus> connectionStatusController =
      StreamController<NetworkStatus>.broadcast();

  ConnectivityService() {
    logger.i('ConnectivityService initialized');
    // Subscribe to the connectivity changed stream
    Connectivity()
        .onConnectivityChanged
        .listen((List<ConnectivityResult> results) {
      final status = _getNetworkStatus(results);
      logger.d('Connectivity changed: $results -> $status');
      connectionStatusController.add(status);
    });
  }

  // Convert from the third party enum to our own enum
  NetworkStatus _getNetworkStatus(List<ConnectivityResult> results) {
    // If any connection is available (not none), consider it online
    final hasConnection =
        results.any((result) => result != ConnectivityResult.none);
    final status = hasConnection ? NetworkStatus.online : NetworkStatus.offline;
    logger.v('Converting connectivity results: $results to $status');
    return status;
  }

  // Check current connectivity status
  Future<NetworkStatus> checkConnectivity() async {
    logger.d('Checking current connectivity status');
    try {
      List<ConnectivityResult> results =
          await Connectivity().checkConnectivity();
      final status = _getNetworkStatus(results);
      logger.d('Current connectivity status: $status (from $results)');
      return status;
    } catch (e) {
      logger.e('Error checking connectivity', e, StackTrace.current);
      // Default to online to avoid false negatives
      return NetworkStatus.online;
    }
  }

  void dispose() {
    logger.d('Disposing ConnectivityService');
    connectionStatusController.close();
  }
}
