<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>منصة الاستاذه نور محمد</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>منصة الاستاذه نور محمد</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>tg</string>
		<string>https</string>
		<string>http</string>
		<string>tel</string>
		<string>mailto</string>
		<string>whatsapp</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>يحتاج التطبيق للوصول إلى مكتبة الصور لحفظ المحتوى التعليمي والمواد الدراسية</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>يحتاج التطبيق لحفظ المحتوى التعليمي والمواد الدراسية في مكتبة الصور</string>
	<key>NSCameraUsageDescription</key>
	<string>يحتاج التطبيق للوصول إلى الكاميرا لالتقاط الصور للأنشطة التعليمية</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>يحتاج التطبيق للوصول إلى الميكروفون لتسجيل الملاحظات الصوتية</string>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>NSAllowsArbitraryLoads</key>
	<false/>
	<key>io.flutter.embedded_views_preview</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<false/>
	</dict>
	<key>UIUserInterfaceStyle</key>
	<string>Light</string>



	<key>UIApplicationSceneManifest</key>
	<dict>
		<key>UIApplicationSupportsMultipleScenes</key>
		<false/>
		<key>UISceneConfigurations</key>
		<dict/>
	</dict>
</dict>
</plist>
