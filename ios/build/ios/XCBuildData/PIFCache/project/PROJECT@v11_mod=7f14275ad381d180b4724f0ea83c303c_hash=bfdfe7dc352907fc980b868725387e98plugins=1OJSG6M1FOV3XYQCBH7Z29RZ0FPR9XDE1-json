{"appPreferencesBuildSettings": {}, "buildConfigurations": [{"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++14", "CLANG_CXX_LIBRARY": "libc++", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_TESTABILITY": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_DYNAMIC_NO_PIC": "NO", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREPROCESSOR_DEFINITIONS": "POD_CONFIGURATION_DEBUG=1 DEBUG=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MTL_ENABLE_DEBUG_INFO": "INCLUDE_SOURCE", "MTL_FAST_MATH": "YES", "ONLY_ACTIVE_ARCH": "YES", "PRODUCT_NAME": "$(TARGET_NAME)", "STRIP_INSTALLED_PRODUCT": "NO", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "DEBUG", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>", "SWIFT_VERSION": "5.0", "SYMROOT": "${SRCROOT}/../build"}, "guid": "bfdfe7dc352907fc980b868725387e98814b7e2c3bac55ee99d78eaa8d1ec61e", "name": "Debug"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++14", "CLANG_CXX_LIBRARY": "libc++", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_PREPROCESSOR_DEFINITIONS": "POD_CONFIGURATION_PROFILE=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "PRODUCT_NAME": "$(TARGET_NAME)", "STRIP_INSTALLED_PRODUCT": "NO", "SWIFT_COMPILATION_MODE": "wholemodule", "SWIFT_OPTIMIZATION_LEVEL": "-O", "SWIFT_VERSION": "5.0", "SYMROOT": "${SRCROOT}/../build"}, "guid": "bfdfe7dc352907fc980b868725387e98c22f26ca3341c3062f2313dc737070d4", "name": "Profile"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++14", "CLANG_CXX_LIBRARY": "libc++", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_PREPROCESSOR_DEFINITIONS": "POD_CONFIGURATION_RELEASE=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "PRODUCT_NAME": "$(TARGET_NAME)", "STRIP_INSTALLED_PRODUCT": "NO", "SWIFT_COMPILATION_MODE": "wholemodule", "SWIFT_OPTIMIZATION_LEVEL": "-O", "SWIFT_VERSION": "5.0", "SYMROOT": "${SRCROOT}/../build"}, "guid": "bfdfe7dc352907fc980b868725387e9828903703a9fe9e3707306e58aab67b51", "name": "Release"}], "classPrefix": "", "defaultConfigurationName": "Release", "developmentRegion": "en", "groupTree": {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98d0b25d39b515a574839e998df229c3cb", "path": "../Podfile", "sourceTree": "SOURCE_ROOT", "type": "file"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98208058217cbb43f43187ab1e9bb28cd1", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/ios/connectivity_plus/Sources/connectivity_plus/ConnectivityPlusPlugin.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a0ec48f1cea2807b5c22054834385008", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/ios/connectivity_plus/Sources/connectivity_plus/ConnectivityProvider.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98cbb835b09fdcc896fd6ac249cd7c338d", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/ios/connectivity_plus/Sources/connectivity_plus/PathMonitorConnectivityProvider.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9887cba724374165b4f3cf4ee4c8cad898", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/ios/connectivity_plus/Sources/connectivity_plus/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98d6c0f6e18d00a70115d18f917f16890e", "name": "connectivity_plus", "path": "connectivity_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989cb84395d1bc55e590626240aa2634d0", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b6076e43a232b0c6ff528e8f0d2cc1d6", "name": "connectivity_plus", "path": "connectivity_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985d0bd8ac910a10019ecb460eae740159", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983a4bd5f99e99d6794d575a8457b90420", "name": "connectivity_plus", "path": "connectivity_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981fc1f3ca26455993bcc7491289c020fe", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9824db8b0d088c68ad3021b2d0eef30404", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9821ea98ee8b65e78cd9916b180d4ca4a5", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9893243b6789dd31485b93ec82166dd1f2", "name": "noorbiology", "path": "noorbiology", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a04f5156d2b905629571a94f9203c7a6", "name": "StudioProjects", "path": "StudioProjects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9869fc9c2b527c3a604abaf6654605f210", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d53216b2647342b528d56484133cd037", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e34c8fd1b59f5f491e9ddf0fbe585d50", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9855d5389142d93d7721b9604c8e87fbef", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983b80a6bc3fcd48001c5270d8bc8b588e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981035a239199b0f2150911012e4489b1a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98668ea0f5068c9a5eb0209db0bcd48898", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9875872195b418dae3f9df29dc5bf2935e", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/ios/connectivity_plus/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e986c22648137770e513416999d1aed5823", "path": "../../../../../../../.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/ios/connectivity_plus.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98f259ce14dd1e015c47fec45d28658b4a", "path": "../../../../../../../.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e982353187c1d963a2b561bc3c46c0811c3", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98921818caf7652cca5c7ec8793f51c0bb", "path": "connectivity_plus.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983e65a6dab97c59fe29c9655867898fa4", "path": "connectivity_plus-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9879abf3dde90ead339591b0043fd5d453", "path": "connectivity_plus-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ada4e178bb8a56ec0c717376139148a9", "path": "connectivity_plus-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986f331950b9bf786398c03618735cad5c", "path": "connectivity_plus-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98efc32f26090b6eca2c34e0b5953b189e", "path": "connectivity_plus.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98b4c3cad5b0c9fcaaf0f8aa17af4c5985", "path": "connectivity_plus.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9815900b6012c0a6bf1196dc9d0cf72b0d", "path": "ResourceBundle-connectivity_plus_privacy-connectivity_plus-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9867eff5d72b72f3e2e578f9142a34b70c", "name": "Support Files", "path": "../../../../Pods/Target Support Files/connectivity_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dd55fdbc37001b1f0192e1b32484af1b", "name": "connectivity_plus", "path": "../.symlinks/plugins/connectivity_plus/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e9809c4ad9d1ff8c3af25f42ad85588d6a5", "path": "Flutter.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9884d09a9a539988eeff181cff2ddca1bc", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e983dbf10779dac9ea5fba7f7e32d100c25", "path": "Flutter.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9865dcad03dce8f267bc71680bf1208bf3", "path": "Flutter.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98c56cb161ab7e4ff45b4a9d6e2c0ea944", "name": "Support Files", "path": "../Pods/Target Support Files/Flutter", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982b2a57acde39bb29857b1f34a233234c", "name": "Flutter", "path": "../Flutter", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984a09544236a2cb34e2b6bd9f5553936d", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/CompressFileHandler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986b7f6b7d49f109304660bef07545f750", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/CompressFileHandler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f42f9b84d4548f57830128aa7a7b4486", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/CompressHandler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983915365a6878ce493ab9ca0a36ddbc65", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/CompressHandler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984a143fdc0d966bb84d2404307931c52d", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/CompressListHandler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c96b73e61e2b5ed17c99f35f8cd6450d", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/CompressListHandler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ed22199190a3ac8a732ea7df73b52ac0", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/ImageCompressPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980e07b97fd1f53c0b26ff532a10df6877", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/ImageCompressPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9859b80a4feb5edc363e1045dac97518ed", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/UIImage+scale.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98dcd8d71a643846a080a2d3e1d106f5e2", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/UIImage+scale.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98adae337420c3da7d87aa9808879ff1dd", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/NSDictionary+SY.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987ce8152aada80bf317c1483dd6363c8b", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/NSDictionary+SY.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988f51508363d42ba84c36b3fde52ce0ac", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadata.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9809379d397fb18bf6367630a7c166b06e", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadata.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98633a8bf829df7337806de56e23d5bfdd", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadata8BIM.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985b1a98dfa60289d672e160e73c2e5d00", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadata8BIM.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c182e5a9bcdb44f5bfd7348ec8e58c1f", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadataBase.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fadaae4317be848ae29ce9ebf867e3e5", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadataBase.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98aa9091c2de31264e4161fafadc51370b", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadataCIFF.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98923a4babb49b6980912ff27be8af1b92", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadataCIFF.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985668eba3670c1e1a281af037f55f6815", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadataDNG.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986b3bbb2c2b51cd0079a63d2477dcbc81", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadataDNG.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bfe0a97187f53ea1f1e9ed224ed46fbc", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadataExif.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9864567d2d1969c4339c17c345757cdeba", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadataExif.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9840e8e93c043bf941a6b6d2f8931f6fd7", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadataExifAux.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987ec053d3d3e4ae6a20de30696c868ce3", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadataExifAux.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9805d15c2a2892c075da7b2b4ab0e1c384", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadataGIF.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e35e3a7e6e2a429daa2bc7d35b1730f0", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadataGIF.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985d2c143823b98ab9ce26294d7ca13d0e", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadataGPS.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98689289ee05aa65270eb7ad30b99ac343", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadataGPS.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9878a76e753969622ea39d953cfd1d2988", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadataIPTC.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9812e186ecef156aefac54ab45506dc837", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadataIPTC.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ee75dfbc2b6d178a46384b46cacecccb", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadataIPTCContactInfo.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98329a085be260c347348afc3d6c57f72a", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadataIPTCContactInfo.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982d520ba33b097013c9705de9217a3b90", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadataJFIF.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98352630cc3e55ff0361fcd746dd72a606", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadataJFIF.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9883d4db6f23a75b7b526bf55235efa6bf", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadataMakerCanon.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980ce4491022b890553194e783b49a7689", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadataMakerCanon.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98168067fb6d1d6aca4901e092f533c267", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadataMakerFuji.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ba6400eef1ee7dfcbd34c00d52f17c39", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadataMakerFuji.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984f81ee22b5012e2ca4f667106752e4dc", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadataMakerMinolta.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f55bda1a70f195d7156c8c19e88d10ac", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadataMakerMinolta.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9896f46b31ba2bd6704a79ead0f51a833b", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadataMakerNikon.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985ccf258e4d69b0f44720bbe02412e4a3", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadataMakerNikon.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a88d898c873d310658f5372e27f7a6c7", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadataMakerOlympus.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98adbd42c713cfda1b8c7cf82d42492c8c", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadataMakerOlympus.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989fd6c7a5f7e2511010ba2fd996384bfa", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadataMakerPentax.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9875ce0c77c3b8503f61f94508e401d956", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadataMakerPentax.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9887cfbd107af9ddd09313f5634fa1e750", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadataPNG.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98368ffe2a79fe847fe6a8f5c0418eb591", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadataPNG.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b072fddfb4e80b07491642084aef2df9", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadataRaw.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98eb9a0327ff7eba4e4fd0a62a444d2561", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadataRaw.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98086f0f35c0cd60837947c48c15c1fc13", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadataTIFF.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98371b66b530c1924f19f7ab7e80dc6a11", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadataTIFF.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9816fe40d60eb09636992482dd7c9171a9", "name": "SYPictureMetadata", "path": "SYPictureMetadata", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98847fb5f40c81184d55f8188fe0963127", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988979aa16cc3b97dfef2f2fb056612198", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98701cd7e1f1212ebf884658d110b204c9", "name": "flutter_image_compress_common", "path": "flutter_image_compress_common", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986cb85f112a589e70ec71f2c62f1d61ce", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d87f6fcdd437c3cac15264dcfb188fb3", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9842eaa70a2d11bead20cea4b790f13967", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9828702f02d07896b0270868b9a774b597", "name": "noorbiology", "path": "noorbiology", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cfc38df03ae3f9ca8741bdd508ee3e53", "name": "StudioProjects", "path": "StudioProjects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e827d6957a8b64d6823d6ac4499e53e0", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983bfc285a99cedc894dc3a7ce9b0bd478", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b92ce8514b69e2ec28fa2b3c6c8f3899", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98457050f1bcd76214ee4b85c9aa54b464", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984d5ef4bd3e0757a9db21d59790a5fe62", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9865852c0b690c79e6db8d16287ad371e8", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e9866350fb01f2576f7038dd8e83e1a6084", "path": "../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/flutter_image_compress_common.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98f15c6c50fd7292835df695ed67d5f4f6", "path": "../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e989a020358bb6235ada9b4b9f7c6113cbd", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e983385201b6f70bcc4e339d15d7cfdce84", "path": "flutter_image_compress_common.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986decf24521f40cdcddf9939d597f7af3", "path": "flutter_image_compress_common-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98ecc1106ab4c9a4be75bac861c4cd1c9c", "path": "flutter_image_compress_common-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980bda784caf8e531d2a73c6510c8fe319", "path": "flutter_image_compress_common-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ca525cbfd3077338368d792e2d8e5b1c", "path": "flutter_image_compress_common-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98c888272dd958bff91da7750ce7d6f7f0", "path": "flutter_image_compress_common.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98d5a1291a0d9410e47cb96d45b4ab827b", "path": "flutter_image_compress_common.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98e7b315cde8978c86459968cb6932babb", "name": "Support Files", "path": "../../../../Pods/Target Support Files/flutter_image_compress_common", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9807cb373e194563cc51c39207522ec408", "name": "flutter_image_compress_common", "path": "../.symlinks/plugins/flutter_image_compress_common/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9837c154160bb6b4fce2c35845041b8292", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/no_screenshot-0.3.1/ios/Classes/NoScreenshotPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9844eb693be4a5fc94a23e2b3a3dd9a33a", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/no_screenshot-0.3.1/ios/Classes/NoScreenshotPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e985f40a2fb4e17db240ae0bc73118e1877", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/no_screenshot-0.3.1/ios/Classes/SwiftNoScreenshotPlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98118fda3e7b68dd9b1e20a88a9e730336", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a3ff3a0e0fdc433e5423e8cbef9d63b1", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982ff4221c82e2947625442db7a24c1f05", "name": "no_screenshot", "path": "no_screenshot", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982ff1fe0a9b39f471eb3364275fa5c61f", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982a64c3edce49d539aaad005caa214b26", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982fc5be079cb54315cf500541f7f088e9", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9841ae1ddb84676a16644048224dbd93e3", "name": "noorbiology", "path": "noorbiology", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9882944da52bf70f6b16d1a5a4313401ba", "name": "StudioProjects", "path": "StudioProjects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d27e9d5867fb2c1abf3650ab641bb83a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a5987b8476d601ce1bc3e7c0e9337acc", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988c18e43959ec7b0af33653db6c2089a7", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9876f90b7431cb4f6d21a4c40c70bb63d8", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987dc5003ba42950570b6a5bbb64cf451f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9800180e431d2df175351434955e459c79", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/no_screenshot-0.3.1/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9832272be1553822234e81d018a949d47b", "path": "../../../../../../../.pub-cache/hosted/pub.dev/no_screenshot-0.3.1/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e9853a378ec804e4e0c12c888b14639f826", "path": "../../../../../../../.pub-cache/hosted/pub.dev/no_screenshot-0.3.1/ios/no_screenshot.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e986ba9d65fd175de7d1f667390f4e62328", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98a932132158d3fc510bff7e1107db19b5", "path": "no_screenshot.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9831172ad2b4f6d11d28512cb6c574e4e9", "path": "no_screenshot-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98e40230bbd619f68ec6e6d88689bf38a7", "path": "no_screenshot-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9865c0c99539754fd0f528d8fd518ec8e8", "path": "no_screenshot-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98810de862576081732a93e46d3a87cd28", "path": "no_screenshot-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98001cbfb1c8b18300342b8b1dac67b14d", "path": "no_screenshot.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98f7268e6db1ba4e19ea7a8e6c86055123", "path": "no_screenshot.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e984a2d40a2826200b77e72171f0b71d35a", "name": "Support Files", "path": "../../../../Pods/Target Support Files/no_screenshot", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9843e088e5346f012ffa4d9ed044b783a4", "name": "no_screenshot", "path": "../.symlinks/plugins/no_screenshot/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98cfeb55ed8c8141c3478275e319e54c7d", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/darwin/path_provider_foundation/Sources/path_provider_foundation/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e984965cfde6d4c6494a9ba5b8f650b40f5", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986a3125b303b383884732bddbd3a7ea3c", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b94f13a8da260fdb3d8a7301c70e3fac", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9894464a1d1a6ca06fe6edfde6f996c9dc", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9812421bb7e8d859553cfbc379de0e737a", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d6b35b7f1f06d0533ee3712eb37179af", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9811e77bcadf5dfac8e55ef342ae08b6cb", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987edf9e70954160230970fd0e1254535b", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984feb648b280bd76b9daac5f6248012e6", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980757c9f6ea3a5e4d8a00f02463989393", "name": "noorbiology", "path": "noorbiology", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982348081a565c0ce6ed24c5f6045b5e44", "name": "StudioProjects", "path": "StudioProjects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98baf813378038fe31f5f27dbf6b37d056", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e96ab9cb91f4ca4ceded6fa796cce809", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/darwin/path_provider_foundation/Sources/path_provider_foundation/messages.g.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c6bf8b5b8527ae76cd4ad90c67fffd0e", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/darwin/path_provider_foundation/Sources/path_provider_foundation/PathProviderPlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f65cc4c90b450f58e89818eebd9098bc", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9867cdb93b754f1f88a54373f03a9c607f", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987fa8d7dfdbd48d702f2c86d459fd9221", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984109ff51831c7297aff5b0aa7473657a", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9817e3ffee6f7a0633ef9d9acad70ccec7", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9801715d302788fcf6697156e0a10dd7dc", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98270545e932dc70439089af9ef1998f85", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e79bcd19bf60fa9b85d4a78c6bdf6f44", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9849592d16882c3e3061e2d7898549d6ba", "name": "noorbiology", "path": "noorbiology", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983a6830f1efffd6edd759fc81b85c3f01", "name": "StudioProjects", "path": "StudioProjects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9845d65dd4fcbb5cb4b653e6b8b6549bae", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983131c07a92165e4b4ef17a7662aed2c5", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b6d60eef6773bf5065e75a433778c696", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983945b496bd9c48e42999affd3dab2ba4", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9822540246e799ce5c8f1b9fed1b015823", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9840634c8624b80cc4826490148e730214", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bd25d04401e850eb5af4024d5dee159c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98da34dc4274d2a583b3c05390b5b4da31", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/darwin/path_provider_foundation/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9854ed7ee12f33aa6b86aa53d0e5a0fc6c", "path": "../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e9827f0e341da97fb6283bc867982d5109a", "path": "../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/darwin/path_provider_foundation.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98985274148a7f07ccd5368b532a04c024", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98e3479fdf4bda4e3ef02e40a66c6d2165", "path": "path_provider_foundation.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98486931f59a4448c911187f04c85eba52", "path": "path_provider_foundation-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9831b66897b68a832d9032b0d37336bd1d", "path": "path_provider_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9818095ac5ec84cda71738e0cead55bbd6", "path": "path_provider_foundation-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98344d38befcff91c8c1d1226c967b83b2", "path": "path_provider_foundation-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98233e826f6e67b19937d677b257cdeaa4", "path": "path_provider_foundation.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e981a4d9a2ebe4a54d3ace474faec2cfa9b", "path": "path_provider_foundation.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9871a646fa42e6e4249bf036c1f07dd3d8", "path": "ResourceBundle-path_provider_foundation_privacy-path_provider_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98d71291e0300c7226a05a80bf1080579d", "name": "Support Files", "path": "../../../../Pods/Target Support Files/path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c8ac7c9d0e725491c4ecea96e4f113c6", "name": "path_provider_foundation", "path": "../.symlinks/plugins/path_provider_foundation/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9819c40656c78c5079176ac1129ca9cb64", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/screen_protector-1.4.2+1/ios/Classes/EnabledStatus.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c9cbbb6ff712a585647e9ec507ce7e65", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/screen_protector-1.4.2+1/ios/Classes/ScreenProtectorPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982327b1f05e6f40da91f4b71f972f7434", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/screen_protector-1.4.2+1/ios/Classes/ScreenProtectorPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f8bfa2d39449d011e6c6a179015cf99f", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/screen_protector-1.4.2+1/ios/Classes/SwiftScreenProtectorPlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98543e96d69d784ca2fa28941163f2a486", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f20f7b08cafe6bbd8f7c9f0e9af4101d", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98aee44d45165f0acc1487c10b32f841a7", "name": "screen_protector", "path": "screen_protector", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9815117bc22eb90de87d24694643571798", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985ac6ad2d17af1d050ad231deade0275f", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9800c00d49b5a87edd3ba9b2f9763be290", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988e385c614915063520226e64e623feee", "name": "noorbiology", "path": "noorbiology", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fc6616d0e56df7967ca70f3e59c2d814", "name": "StudioProjects", "path": "StudioProjects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9863413b2e70dd4f53255e7d8ffe9147c7", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984bb03067274d2b026c45f3c393ed46de", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f15cf60fecea6792e6c28f22247782f0", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98852f7da0446ecf8f88d1eb4218cfa8f8", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b19df5781582adc619ce93ba68f8e4c7", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9802b4752b4882cd0436039fd89263e48e", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/screen_protector-1.4.2+1/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98917353c3b145974c77a10e09e03a5cdd", "path": "../../../../../../../.pub-cache/hosted/pub.dev/screen_protector-1.4.2+1/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e987aade842da4b22a7b540316c8d069b82", "path": "../../../../../../../.pub-cache/hosted/pub.dev/screen_protector-1.4.2+1/ios/screen_protector.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e984d570ea9fbd82c1ba9ec54ad2645e22a", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e989ba9bff4ab3744ebd195cf77265d6e15", "path": "screen_protector.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c0bd8607209b1a72f3232500df8a2139", "path": "screen_protector-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98ce9d4a7ef96674a1266b9a224f86a5eb", "path": "screen_protector-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989add26c33901e2f340802a92b2b7631d", "path": "screen_protector-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987f681cf6a46ac21d9c45dbcb95ed8e75", "path": "screen_protector-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98ae06e881384da7a6b33b176367647201", "path": "screen_protector.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9822a7d350d2088ba4ac3d19112a9dc2fb", "path": "screen_protector.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98caef19e072565b4e41f9b7443e3d9806", "name": "Support Files", "path": "../../../../Pods/Target Support Files/screen_protector", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e8fda9dd87d4a4026e18d9d9ee6bf4ec", "name": "screen_protector", "path": "../.symlinks/plugins/screen_protector/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e984924daa07d4d97b9c031e4960b2677aa", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources/shared_preferences_foundation/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98698446bd7ab9de36a8de8bc88a631d68", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d2ca9d7f18825db7a43654ab53b6b30d", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981f16fdf00fe00a20be6ce94140042a7a", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9805fe6212c8b6af075379650737c1259c", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ecfcea38983c0eff320d7db700bcd763", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987d782d3b98fd701e28915398d734d5c1", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987b5f6a3fb9ce4584e3010ee15e1c2a90", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98adaaefa6e8572b79398eecad501e123d", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98661bb60989a89c1da4d6498fe812eae1", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a78672da56b0cbcfc68972937d2e38b0", "name": "noorbiology", "path": "noorbiology", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983fb2438f755ee4733a8073301c4e0dee", "name": "StudioProjects", "path": "StudioProjects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cabaad63fe7e109c730ac5f3204fbab4", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e981b21f31014b1b868cbca02fe8ffdaf83", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources/shared_preferences_foundation/messages.g.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b4dfaaa7e9c523729233999276921be9", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources/shared_preferences_foundation/SharedPreferencesPlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e981c32870a628b2f99bb5ebf679f85ad3d", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98aa8d4706866ea74be550d0dcd0c4a4b6", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983e0c3dd1ef4fa47b1172e8d31fb9cff2", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982ef7c3a4f4c6fb4d085e63732860f144", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f8a8c50551640d023f2daf52d7e757ca", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98942d3bd50fbddc8111956eaaf91ad171", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98543c3e846af0faff527588981fecb81f", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988a37d7ba405e2f67a77d9b8bcf59faa9", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98da3cec0e67b220f499dd8661dd32abed", "name": "noorbiology", "path": "noorbiology", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dfa8e8c7fc3f82854c77a55f9d16ed10", "name": "StudioProjects", "path": "StudioProjects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98273f0493ff57b0676cf59aae286ec20d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fef31711677d44c6acdd283e7d1d9777", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985704c43e1480839651d62faf002b1279", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e8f39eb81fc849322422172054e3f322", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fef039553b4246483366c2f4cab49804", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987f4aa85533bcce842f1d7adb005ab934", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a58904c026d249a1adb6ee6cf08cf05d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f8c6e57a06313a6a3f5b22859ce3805a", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98dc34b7225fa367571f0281643efb4cd5", "path": "../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98a665451d463a2ee2d2ba8ee67e96cdf7", "path": "../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9892971c27039cb7cb310352b669d9f1b1", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e984289a553248cb5a32978a798a4027827", "path": "ResourceBundle-shared_preferences_foundation_privacy-shared_preferences_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9801f82e8de19577faaa82eb9575e5d080", "path": "shared_preferences_foundation.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981baadd577cb64b9709312eb4241edff7", "path": "shared_preferences_foundation-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9881d2c15be28263b147d2672bdf28c751", "path": "shared_preferences_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9819d3adc11ed119d7ee0a2e08f2038e53", "path": "shared_preferences_foundation-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fb6e76165277aef818a7fae278a9a2cf", "path": "shared_preferences_foundation-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98d079931465c0eb7674afefa200cbc7fa", "path": "shared_preferences_foundation.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e981e5fa5ee49cdad91f70f190434265027", "path": "shared_preferences_foundation.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9863e66c34d0c7cf9db1dacd6e461627ee", "name": "Support Files", "path": "../../../../Pods/Target Support Files/shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dedce7283f3f078939ea702277f0d3a0", "name": "shared_preferences_foundation", "path": "../.symlinks/plugins/shared_preferences_foundation/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98e40e29743b57573e354b7a98c2391a8b", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/darwin/sqflite_darwin/Sources/sqflite_darwin/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e986994324e43caafa27bb2f9529ed35d35", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9896b3a8835d2aaa31d0d2402cae1ca490", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9894ab05f4298eda3d80605f81556b97fb", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981f4f7daa743a95e9bc85600262f25214", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982f634a30364c217cf3e10aed44ff90df", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981c8e908bee77d74f8065c3a5a78b4a2b", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ca92f5266f2c17c348afbaf29d05d85f", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ce32a4a3fff0a57dcdf323d7b56bb704", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982247d61e9e4c1d4d3af2405d19b2b15c", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9849a87d1b5d76604ece5a231cd3cde74a", "name": "noorbiology", "path": "noorbiology", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985454a37d8214a4f34dd66d86934e7640", "name": "StudioProjects", "path": "StudioProjects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98452066131a549ddf3d343b5e9b188560", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9855ba0e1fb90a752ca41b313a08ed99a0", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteCursor.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98584cfb8996e5cf28166e4d8dcfaefaf1", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteCursor.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b3222b191b72238e48394c3ee31ad576", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabase.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980c15021ace222ec3b99a452ac4e006d1", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabase.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982bb4ef84ce7170270c9abb5fb9d39c6c", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabaseAdditions.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987fbff7bf3d4033b446f1b6ab40fdf5f8", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabaseAdditions.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c6d4e7b623888cae41addc2a4f9bc692", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabaseQueue.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98953c0982a96ed599b079ec524eb60e5f", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabaseQueue.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c0270625cd42e2085f2fd9520753976f", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDB.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983ff496897fc68ba0480964b75a62cc7f", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinImport.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e08137200ee44e62d33e7fffab03808b", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinResultSet.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98951f661b9cf241e7e3e1a8ec6068feaf", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinResultSet.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ebc2f718a9e6490b4ccbd0c8c7b5d5d7", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDatabase.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9897dafb072f0ed5e0b9384a4806bb2193", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDatabase.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98060db2d2a63e5a5aa40aed0b04d50d6c", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteImport.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9811923c4a5ecb9df12e29053cfb46f54c", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989be8fa758246f06175c5a4ed227b1a18", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ad6322238a3b6f0ec0d6acb369fa3394", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqflitePlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9819db52c94f2a4929436c33695935d307", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqflitePlugin.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a8fbf3017b13973f8bdfd5df1765ed32", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/darwin/sqflite_darwin/Sources/sqflite_darwin/include/sqflite_darwin/SqfliteImportPublic.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980a12bde285b175a9added6c6fea18b20", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/darwin/sqflite_darwin/Sources/sqflite_darwin/include/sqflite_darwin/SqflitePluginPublic.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98b23d8bf401a651979aa276ec4675f595", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98edec72333b32a735f490a2b7e3393975", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983eb7a708f43e4dcaf65ecf4fcae04a9c", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d941d59a9d244ea6c6143c4867967e76", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9867dd731d8673d43290b6532fa200736b", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984b448284f1dc740e4fc56d8c426783bb", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98756fc69fc28874fc35a6363a964f23cd", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cdb186c61292934ce1e365910644db06", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9873328fe27d65e8dfb310c1f8470ac345", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9889c002df2e1d272fd72c08170f5611a3", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b582813ea937e8086ed6bfc601bb548a", "name": "noorbiology", "path": "noorbiology", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d18e3fa408556207613fd2b4c55b2b3c", "name": "StudioProjects", "path": "StudioProjects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c45e292f04fcd158992c507c3245258c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984b02862bfc75f0ad42050ac1af420d36", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9809c4110ffa27afaef16af8faab8c3da2", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d921f7838657410040086f9e19e598af", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a5665fb9aecf2bfa2623323497f3b91f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98796653c76a6c1ae2a7a7b7e465fceedd", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e94c39b4e53eb4640ab7b1af7e731a4b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980a324171a8bd42c5a55c0642a1cd2854", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/darwin/sqflite_darwin/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e986ba575569e403345398899bae428c162", "path": "../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "bfdfe7dc352907fc980b868725387e988acfb9b95184f37aaf381cfe30ccbe72", "path": "../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/darwin/README.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98189cb92fef2e80170038907c69816d50", "path": "../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/darwin/sqflite_darwin.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98a836c5d8870347d6875ef2bc26d96132", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9809267fb5ab5c62530ae0a8192536c765", "path": "ResourceBundle-sqflite_darwin_privacy-sqflite_darwin-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e987d3db2a489b6b46b3bb654f3b681852c", "path": "sqflite_darwin.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9857fee526a328010075f35422b0c64b6c", "path": "sqflite_darwin-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98315738815f1f8fbc8225a28d6138d5be", "path": "sqflite_darwin-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9808f0b2a4787c816997fe31c4ed28a521", "path": "sqflite_darwin-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9846e9ead07ffddc717b4b0f94c7582338", "path": "sqflite_darwin-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9881fd9103fd8e7b9ee4c9803321f25e9d", "path": "sqflite_darwin.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9846416764fb2dc9f3738890c514eb03ba", "path": "sqflite_darwin.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e985e8d6128ae258bc2fb1babcdef62909d", "name": "Support Files", "path": "../../../../Pods/Target Support Files/sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986e68bcbc789528222a304a00b6c4d1c3", "name": "sqflite_darwin", "path": "../.symlinks/plugins/sqflite_darwin/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e982d1c21679bbd084d8162aa2b3f5807e2", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.2/ios/url_launcher_ios/Sources/url_launcher_ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98c9507b56ad8e220aa2cbc816d103bdf1", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98aef7a0ad1612e74528b564fec922c050", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985c10d413c027ad618ef0fc648afce014", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989dba71fa9e9a252098dd92617fada71a", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9891a719337f1b1e2fd79a2269f0553708", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9800d8bb559486075d42b46cfae4d252b1", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c1437b1a15a308c2a8f21368610348e4", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f4e7d891cb9ad10ab5cd1b4d65788249", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981458a2b21b8bca93b62fb0850f3cfa37", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b833e3c4910dfa874e4b8bd4752e3779", "name": "noorbiology", "path": "noorbiology", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9806e0cf7e1780df7267232f45a350e2b1", "name": "StudioProjects", "path": "StudioProjects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9880eaa85a9a2be55abd15631559b4a0f2", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a7f973a01425502202fdf638ea2f0bae", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.2/ios/url_launcher_ios/Sources/url_launcher_ios/Launcher.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98102dbb4d5a1dcaacbb8fcb4f25b7e9fc", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.2/ios/url_launcher_ios/Sources/url_launcher_ios/messages.g.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e985709e21686f401a356b74be8aa9b1bda", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.2/ios/url_launcher_ios/Sources/url_launcher_ios/URLLauncherPlugin.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e988f80972bb02c2e93a569311afdc743ac", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.2/ios/url_launcher_ios/Sources/url_launcher_ios/URLLaunchSession.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98168975404fd992e819edcc822c4e856b", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98792b164d35763af1db563b345a0a9662", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987ea867ada7aa3d43354d1ee3daff0b58", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98613e2f38401a3597f859dd1834de735b", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984df1cfeba42ec0ab0a8ac2b8fea23f73", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a1e4ff7d50480afe1a25378a9ea8c2d8", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98510435ce140daadf374e6ddac246a185", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9886df9eddccfff94379a2cd4a3a458778", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982d2e82c6352dd2426a77a4bafb68026c", "name": "noorbiology", "path": "noorbiology", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981c32f4373c6380dc1d367dceabb39d7b", "name": "StudioProjects", "path": "StudioProjects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983f80d475c0e53fedba38697adb481077", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988637c3629560acd22ddb118de3ba26b0", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987986ce4a9c388f2892b868b8f91f3007", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984b3efca895217351148aeaefa2b50677", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98947b64a1e43e612a29d92ddafc057f4d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98047383bc2881a8c7f128a6dcfffeb6d2", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987c22d151ae6714c4f04538d55dc52673", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c8b30d5b0bf7da5e2b40943c36a64005", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.2/ios/url_launcher_ios/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e983639fdcdfbb61c123842d497afef21e0", "path": "../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.2/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e9861f288f5798ee3371ce7e4b9d03bf54d", "path": "../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.2/ios/url_launcher_ios.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9894521a647a215c71969ebd4206b16905", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98e952a99a0ae4a644c2c608d49c988743", "path": "ResourceBundle-url_launcher_ios_privacy-url_launcher_ios-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98b334e08ca0a7b3fefaa82aca978f6c46", "path": "url_launcher_ios.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9815f14e8492c955fa0fe3e6a72c92be17", "path": "url_launcher_ios-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e986effbeac7693f7497ba198bfc31b28be", "path": "url_launcher_ios-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982032a9d5c57ed5d1c2264c43b213d1ed", "path": "url_launcher_ios-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c8cc1fc700fecc2f4f167d5cf05c445f", "path": "url_launcher_ios-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98a5a1c21b5ed26e65db6a7653b660ee48", "path": "url_launcher_ios.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98395305aeac732a454cfd3ebe38422dd1", "path": "url_launcher_ios.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98e0bc18c3302994b8aa370babd8097bae", "name": "Support Files", "path": "../../../../Pods/Target Support Files/url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9853adcc918c5f17c29c4eb4f02754bdbc", "name": "url_launcher_ios", "path": "../.symlinks/plugins/url_launcher_ios/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98f57f1c1e46ddb974cd9e8e5df562102d", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.18.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98adf917f2e221c277caf6a25b87d34fab", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bbc06d7f67c8d5646410dc1498307ffc", "name": "webview_flutter_wkwebview", "path": "webview_flutter_wkwebview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9828ad60fb7c72cc5fce935fd062a19bc0", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9834482014a76d13cd26b7f5eac22bbbcf", "name": "webview_flutter_wkwebview", "path": "webview_flutter_wkwebview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98af20517d9fcf7c4c5d2d6fe66350d1c3", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b3896338a08eab135ce1da14659ad5dd", "name": "webview_flutter_wkwebview", "path": "webview_flutter_wkwebview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984ca45f06742831bb33868b7c6f4d22bc", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983736e7beb9e2979d1612c50c603efd21", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987b79654df07890c13c80eff03ac22f55", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9880d277bbbedfc17bdb97ee95ece987b7", "name": "noorbiology", "path": "noorbiology", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c3a33213a50ca12920c35f9fa3cb51c1", "name": "StudioProjects", "path": "StudioProjects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982ff9f2c8e237e227fd23dd6b2aed41da", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c60bbc6e9428ef0023030ad061d20c62", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.18.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/AuthenticationChallengeResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c1811f47ec8b0a3e7c16ee909e9e3ab4", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.18.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/AuthenticationChallengeResponseProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986a47be928a3c002a0673d446e37af3e5", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.18.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/ErrorProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9819c965cf76915ea6aec9c3257055dffc", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.18.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FlutterAssetManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e985328ece09c4d3e48794a0288277950f7", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.18.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FlutterViewFactory.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ace0a64cc3dfdd87c1d629ee95ea1f0a", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.18.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FrameInfoProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e8d13d674b336211f99b6bc4ea230e2d", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.18.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/HTTPCookieProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b2bf0554ce8427719c558d9dbaa9e887", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.18.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/HTTPCookieStoreProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98bc72a3f843f9a1b7295f2b75f05ea7b1", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.18.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/HTTPURLResponseProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980a7545124a4e3af91c692d4d321a1573", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.18.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/NavigationActionProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f278ab2c0a0ca06b6d7ecd6bae299bd2", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.18.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/NavigationDelegateProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f73480c394dbb0efa271e52bf07c77ea", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.18.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/NavigationResponseProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98257acf164d59c47675a813882dfd029c", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.18.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/NSObjectProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b34b835e1fc5053463a58d3d09ed805a", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.18.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/PreferencesProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c1d73464b9f9c1f9a06cd0de85568e1b", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.18.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/ProxyAPIRegistrar.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98170c1349269a8a4052f819538884c471", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.18.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/ScriptMessageHandlerProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f154208db20283ddad0d0eb4b970a1c9", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.18.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/ScriptMessageProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f4ccf4084b0f9c57799061b876da9d9e", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.18.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/ScrollViewDelegateProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986fb8039c6555e022700a17a35a1f8cf2", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.18.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/ScrollViewProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983f37b570f973ce10f309dcbe0f3a913c", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.18.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/SecurityOriginProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a73525390ce650719d9680126c09e3a7", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.18.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/StructWrappers.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987d6ed8e51c52ea8297e46c3e90cafc67", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.18.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/UIDelegateProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a685647dc68a69a132761c64f590c539", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.18.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/UIViewProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e988bc950b4b17a3b6490862521aa076b9d", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.18.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/URLAuthenticationChallengeProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98cd39b88d3ada50510cf467dcf6794b56", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.18.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/URLCredentialProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f8da8f0c424d9555060bb8f99e623941", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.18.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/URLProtectionSpaceProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98de5141f5f2b92ebc5322f66946a48438", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.18.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/URLProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98268b84883bfda5ab9f675b849bd16bb5", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.18.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/URLRequestProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9856e74174d13b77ec6dc2503d4ac764cc", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.18.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/UserContentControllerProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ba4b93cbd4461c459fea002ad80ad50b", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.18.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/UserScriptProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9883262d4af7098d1581cb6d71b4ce3dc0", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.18.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/WebKitLibrary.g.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d3abb8a9d44fb23dd82696069c5afe5c", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.18.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/WebpagePreferencesProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f615a41b5f5934d9f5e62bf073794901", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.18.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/WebsiteDataStoreProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ba26fcd13a03301ec5f5af4ec7da321b", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.18.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/WebViewConfigurationProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9888b9c161bba790f935f709754b206ec7", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.18.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/WebViewFlutterPlugin.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98589e7af0ea39032142b118c6e67e4dde", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.18.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/WebViewFlutterWKWebViewExternalAPI.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986e794cdd11b37ebcaead6ee9920d4c90", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.18.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/WebViewProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e986ee808813e4404f48e342b3fa0a0a2bf", "name": "webview_flutter_wkwebview", "path": "webview_flutter_wkwebview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fffee5ae9bac15cfc805d86506ab02a4", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ca2d7b9a57f15a83410f909e69b9d66a", "name": "webview_flutter_wkwebview", "path": "webview_flutter_wkwebview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986aeede10bd5c0c7d683560d445f80fd3", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ffba808ae579724e05c6342e4414b79e", "name": "webview_flutter_wkwebview", "path": "webview_flutter_wkwebview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a5838ce6bcd75a6e1b52b039f8744d1f", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d052c9201f2d893b5e36a832c7580e7f", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985920df53a0b82ab8e2043645a0f7e684", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984a8ea4f206bf96ac52325a86516c32dc", "name": "noorbiology", "path": "noorbiology", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ad4ac8bd2806d273c48c786fbf482be4", "name": "StudioProjects", "path": "StudioProjects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98df4fc7c4bec10aef0d8b3d2137f35129", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ab6bc6138be81e12e4f816cb6e15d039", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d4cd8acbda945d711923a77f03ba2ff2", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982eb6969cd11c117a4b59921da74273fb", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bf3db54870b8824bfb1de346021d7c76", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98469230bd9926a8e3a74ad1ad35329561", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ac14a112e49b028c2a71815ec80938ee", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985e6d2486f1929796d8b6710a7c94d51f", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.18.3/darwin/webview_flutter_wkwebview/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e986a1966b662ad3f13bf58d022db4ba9ae", "path": "../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.18.3/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98e89a5aeea116d21a8c55a64196861335", "path": "../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.18.3/darwin/webview_flutter_wkwebview.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98dccfd1407fc674ab91dde43667f31ab7", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98cf48d69db397a6b2ee2b9627cb9831c7", "path": "ResourceBundle-webview_flutter_wkwebview_privacy-webview_flutter_wkwebview-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9878f90fed1f2a22b246d75a70236d8b00", "path": "webview_flutter_wkwebview.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980f419aad6544098d5fe70e411e211ccc", "path": "webview_flutter_wkwebview-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9842ccac1fb261bee69f08677a18bbe644", "path": "webview_flutter_wkwebview-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98431f28ad39b1429174ca5e1e29d75855", "path": "webview_flutter_wkwebview-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983b08aca9a405fd2ac1408c5b60b82da8", "path": "webview_flutter_wkwebview-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e987a7077cf10d8b52fb1035348436af02b", "path": "webview_flutter_wkwebview.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98d5b31d94a0dd62c84c2e605ff4015dd4", "path": "webview_flutter_wkwebview.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9874474a8407286bfcd4d754d8a571e0ab", "name": "Support Files", "path": "../../../../Pods/Target Support Files/webview_flutter_wkwebview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98af2347ed4ee6cacef1b43814339d0300", "name": "webview_flutter_wkwebview", "path": "../.symlinks/plugins/webview_flutter_wkwebview/darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98918eeb39a57d27ad0ccecd1d2ae49067", "name": "Development Pods", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e9820753baf2bbe3b4d34284c7cdc004651", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/CoreGraphics.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e987169fcd29c627f30e820b00793706b3f", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/Foundation.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e98f1de6918a7ee8f49634bfb8452b2265b", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/ImageIO.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e989a8b19cd316e36476e81fc60d3b22285", "name": "iOS", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980db8d57e471489ba790f1078d8cdd411", "name": "Frameworks", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e988037a725bb80084750f4b871a722955a", "path": "src/demux/anim_decode.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e981e0932050ed3d08ddf307a661c11fb59", "path": "src/demux/demux.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982cf4f8252f2be485faa45d0c8a57c015", "path": "src/webp/demux.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e982beb5a8d7a81fe21ecff5d439f3f3877", "name": "demux", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98332b45b19fee89f84416abfd229b5847", "path": "src/mux/anim_encode.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9826a891c4eba7ffa27c0bbda494f73591", "path": "src/mux/animi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9884aace1df1aaa8f9d5f30d00ed2d532b", "path": "src/webp/mux.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9877515ec118b442162f4bb6718ca7cbed", "path": "src/mux/muxedit.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981e9d48519103dd987234c9710226a10d", "path": "src/mux/muxi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98a7c108bf38422e98ea90b995cd60ac3e", "path": "src/mux/muxinternal.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98ac41b39614b8338a647d0c02a05b966b", "path": "src/mux/muxread.c", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9894962c3eebe166f628e61373828a963a", "name": "mux", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e987625092fabff32809c9627641caea7ea", "path": "sharpyuv/sharpyuv.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983293df5678421d3cc99030ccda9c5785", "path": "sharpyuv/sharpyuv.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9803f793641586a15ca1791b8d39fbc052", "path": "sharpyuv/sharpyuv_cpu.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988a11a5e08ef7219cfecb935999bdb5bc", "path": "sharpyuv/sharpyuv_cpu.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e982fc1327bbface990e6274195e92108a4", "path": "sharpyuv/sharpyuv_csp.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985ba2ffbe4a39e654975d11527d3a384c", "path": "sharpyuv/sharpyuv_csp.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98eed87a5c5bc0b59a24d3999c4fbb5f4a", "path": "sharpyuv/sharpyuv_dsp.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a1d23df35afba237a9bc8b1f4658b96e", "path": "sharpyuv/sharpyuv_dsp.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98512fe29ea7c0e28c96c3c90102649974", "path": "sharpyuv/sharpyuv_gamma.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985b6e3d2aa57436920fea7eaf6406e156", "path": "sharpyuv/sharpyuv_gamma.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98e9a0a61d19a2d74f4dfe4f4a8f67d49e", "path": "sharpyuv/sharpyuv_neon.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e986d1278e59ce285f46860d8305df87174", "path": "sharpyuv/sharpyuv_sse2.c", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98a270abe031a9e5860792aeb52ece12b6", "name": "<PERSON><PERSON><PERSON>", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98a0c1c99dbd54e41e2228ec6520a0864b", "path": "libwebp.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982fc79dff8a771c454423e329b8dfe0a6", "path": "libwebp-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98d74868365baf9c3c9d90329e0f7cffea", "path": "libwebp-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9830c36878cec27473095dd732a9496a39", "path": "libwebp-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9829f5476a5610ba1c8686035827aae9df", "path": "libwebp-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98773a2e60e1f302ea1d1cf5bf7fea3334", "path": "libwebp.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98204b67e8ef9e8b4d8c75509e611842ad", "path": "libwebp.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9870a25aa98068e656bbc081049c28bcb4", "name": "Support Files", "path": "../Target Support Files/libwebp", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98030a4baaa748a5ebbb88a677251945ae", "path": "src/dec/alpha_dec.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e982308a8378cc823308f793590cd546488", "path": "src/enc/alpha_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e983feeffecf5627788037e9ad103391556", "path": "src/dsp/alpha_processing.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98be7c479ec74f673b994d849492288265", "path": "src/dsp/alpha_processing_mips_dsp_r2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e989df5a77254d2beb32ea47909d7f1f63d", "path": "src/dsp/alpha_processing_neon.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e986d5a063fd39222e01d555682a940a2fe", "path": "src/dsp/alpha_processing_sse2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9827fb7422f3bc61029d42e4263b4852d5", "path": "src/dsp/alpha_processing_sse41.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98eafdb7943479ecf582a770bfef2aeacf", "path": "src/dec/alphai_dec.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9804f82b1125855843e53dfddc71f6ca2a", "path": "src/enc/analysis_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e989fcab0bd375d2464a742333fc3eea8c8", "path": "src/enc/backward_references_cost_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e989bca88946e30d04b8e6872a3dff98741", "path": "src/enc/backward_references_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983b30c9520a59b83924eac8ad6596b898", "path": "src/enc/backward_references_enc.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98887c3d4e5c9c94137512db167f5df8c9", "path": "src/utils/bit_reader_inl_utils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9867b94aa3fe530ef782756462a240f26c", "path": "src/utils/bit_reader_utils.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983d9e63e4fe11bf394388e9c6ac659619", "path": "src/utils/bit_reader_utils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98f7b6951397098d255e037d598bd26c00", "path": "src/utils/bit_writer_utils.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983b1182d6bb36cbe9cc89928436b40e1b", "path": "src/utils/bit_writer_utils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9813868a991de4f1988faad74a02df410d", "path": "src/dec/buffer_dec.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98af4b9caa59b1d82bcf5ff0df393d025f", "path": "src/utils/color_cache_utils.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981154d36b01ec075af376804d03b62d58", "path": "src/utils/color_cache_utils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983a56fd4c2c108f28b53511a70f02d105", "path": "src/dec/common_dec.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d6428d08ded3206060512f772cdf3bcb", "path": "src/dsp/common_sse2.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9883f082222d675aa704be1bed6908ed27", "path": "src/dsp/common_sse41.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9810221ef4d91ff79275a064524bd98903", "path": "src/enc/config_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98f2efa657f063a335f8886bece4a8c19a", "path": "src/dsp/cost.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e983624e8c27cd1241427ab97e09ab63896", "path": "src/enc/cost_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9803ba0fb0040fb68a6832280697eb5e17", "path": "src/enc/cost_enc.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e985b584113580fcebdace704dad9f7149f", "path": "src/dsp/cost_mips32.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98e9a215791eaa356aae22e7ae2bfd4b60", "path": "src/dsp/cost_mips_dsp_r2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e989b80801132b4771e64c2353bdbcd794c", "path": "src/dsp/cost_neon.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98505094b7c9a54a9b76b5ece14f539f38", "path": "src/dsp/cost_sse2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98e4fc730485784ba05c34c7b4cfae0628", "path": "src/dsp/cpu.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9806ad168feabefdbe300c3a6dea2f0a02", "path": "src/dsp/cpu.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98d7a0bf1a28a9fc66c54af57201df8d5d", "path": "src/dsp/dec.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98f535591b767e9dd5e1d404073c4dff4a", "path": "src/dsp/dec_clip_tables.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98c53653c59354e51846a70f6435d1d449", "path": "src/dsp/dec_mips32.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98898ab125d5ad85602a5c03f572c5e150", "path": "src/dsp/dec_mips_dsp_r2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e987d5854e0e4c47b920c88870c891a7e5e", "path": "src/dsp/dec_msa.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98903c9457245444c378b4b299558f62d2", "path": "src/dsp/dec_neon.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e987b10eecc545a768e1d0a5592e2e40681", "path": "src/dsp/dec_sse2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e982dd277015bbffda12b84b0fab5a011ce", "path": "src/dsp/dec_sse41.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9800712033ad4cd00493bf583a5ade8c41", "path": "src/webp/decode.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d36fb7bc4ae294b8ee31f189e2eb649a", "path": "src/dsp/dsp.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e987b1b3d8d37541e099730d34de6726ecb", "path": "src/dsp/enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9887843b40614515152c938e698504a8ed", "path": "src/dsp/enc_mips32.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98b3104e0b0be6013a20ac173b0d343fe4", "path": "src/dsp/enc_mips_dsp_r2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e988d0de1c7967e31d6ff646cf53b19b9ec", "path": "src/dsp/enc_msa.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98edc78935291858e6acf92c1d5aef027e", "path": "src/dsp/enc_neon.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98808e1bef7eedc7a24dd9b41ea2b8bbc3", "path": "src/dsp/enc_sse2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e982094a570cc23969dca5808e5a685bd90", "path": "src/dsp/enc_sse41.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985b3105504ddb3e901d73cc1aae916630", "path": "src/webp/encode.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9825288b792e9df8e46233ac82d7c317bd", "path": "src/utils/endian_inl_utils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98fe25086f0c23555017b83d844a403428", "path": "src/enc/filter_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98eac9b9c78170329f67fddb98e684dba5", "path": "src/dsp/filters.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e981e21cf23695292bf0d4c1f9054d3a45a", "path": "src/dsp/filters_mips_dsp_r2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e984d577a7913d0668dd2cf44e101d13503", "path": "src/dsp/filters_msa.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e984646387c88a0f12f5922f9ec55202708", "path": "src/dsp/filters_neon.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98eb8b9a5589dc4e915486985159619bd5", "path": "src/dsp/filters_sse2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9831d4ff3c95d1793a93c22172aeeb376a", "path": "src/utils/filters_utils.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981eff39f0cddb5d42684945d47298c923", "path": "src/utils/filters_utils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9842e9a0e78f1e817df750fbd8a57d4235", "path": "src/webp/format_constants.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98d55a7c4353831353f5cadcda79a56b7b", "path": "src/dec/frame_dec.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e981da80198f665e9e18e1b85d609c76f43", "path": "src/enc/frame_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e989832b4f25884a62e28a734492890f00f", "path": "src/enc/histogram_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981c3d66de0763517387ca0170554621e6", "path": "src/enc/histogram_enc.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9877e5ba7f745cbff9123567fdbfb45954", "path": "src/utils/huffman_encode_utils.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cdbeaf73fb829555ac6ef9a06069d4f1", "path": "src/utils/huffman_encode_utils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9834094cff9aecb53cdeffd4bfde3317e4", "path": "src/utils/huffman_utils.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980733267c1466b1054c0ee37e790fd2ec", "path": "src/utils/huffman_utils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98624c9b7ac7caebb30b8dbada4fa6da70", "path": "src/dec/idec_dec.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e987901a54583cc3df16e3ec217353f5341", "path": "src/dec/io_dec.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9827055dd454135f873f71b74080925df0", "path": "src/enc/iterator_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98ae89c685ca03abb3d3f03ff0b4becd29", "path": "src/dsp/lossless.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e4c06943bd670459458d10a22bd12f06", "path": "src/dsp/lossless.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9887507e0139c26d4d108b13df339351a6", "path": "src/dsp/lossless_common.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9814fd9917b07da7bcbb43f4c5910a787b", "path": "src/dsp/lossless_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e988f56fa78e0e97e1b7d5be1507a402a8c", "path": "src/dsp/lossless_enc_mips32.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98a81f18b2ab5615da9fe0d2b14ecc4be4", "path": "src/dsp/lossless_enc_mips_dsp_r2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98b8734bb27b1905877339632a0ca7e1f0", "path": "src/dsp/lossless_enc_msa.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e982001d5af4ab0bbb6515d599298195b72", "path": "src/dsp/lossless_enc_neon.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98808923510f0a81ff2e0d3b3aa4c2bc2c", "path": "src/dsp/lossless_enc_sse2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9809311629ca49b2af18613ffadcbf6196", "path": "src/dsp/lossless_enc_sse41.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e989d369425230687266e5a991362bf3fef", "path": "src/dsp/lossless_mips_dsp_r2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9855c6ba93c7f2050cdb11e805f7464d77", "path": "src/dsp/lossless_msa.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98c0294c802dcd0ede8ed85a809c5701aa", "path": "src/dsp/lossless_neon.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98b44198f97cd7f2aeb7c5dc32ba583c4c", "path": "src/dsp/lossless_sse2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98e4e5f1fd6207edb781f42fa5d593fcfa", "path": "src/dsp/lossless_sse41.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9809d19685a25cbb627a371da674752a93", "path": "src/dsp/mips_macro.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983216df238edef0f9d6e0a6bd122d855b", "path": "src/dsp/msa_macro.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e489dc26d05a89a9a82ad034fc63ed62", "path": "src/webp/mux_types.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98108bec0e377fb4e9fe42b0f73755c765", "path": "src/enc/near_lossless_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98720aef0016d8d8260d15743e50142c00", "path": "src/dsp/neon.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9845e016404dc9d2991bc26e9521fc7632", "path": "src/utils/palette.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d3b9980dc733d2802f8bf36ac4d1dab5", "path": "src/utils/palette.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e982f0f7df0ccb43b6b195df9b1d169ba74", "path": "src/enc/picture_csp_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e987ecdd5f7ddd6afd2264231087161db65", "path": "src/enc/picture_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98d31cc7d9008cae90921a53c5bd529e9e", "path": "src/enc/picture_psnr_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98b28aafb875c7467789dbe12d10c316e1", "path": "src/enc/picture_rescale_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e988d41525adff068e08dec1aa0ac0145b4", "path": "src/enc/picture_tools_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9856652fe75ee29d9b7806030bf6703f77", "path": "src/enc/predictor_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988aa65a2e6de61f3147966c4f7200abd3", "path": "src/dsp/quant.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98f40785facc3a835358ded75c92ba9c45", "path": "src/dec/quant_dec.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9872ae4241ab5a69d6818c092182e41da8", "path": "src/enc/quant_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98822dbeab438f245dda657a7465291151", "path": "src/utils/quant_levels_dec_utils.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d19e44e6b8a420c31cfc690c0ea43114", "path": "src/utils/quant_levels_dec_utils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98bf5ee86cbcbdc7cf1b54d3f9e260deb7", "path": "src/utils/quant_levels_utils.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98373d7bdaf814c2b91555e8be3882310c", "path": "src/utils/quant_levels_utils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e982e341e1de34fd036d36286e6c3096d5b", "path": "src/utils/random_utils.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bd75506a52fa53a1deaae0665766103b", "path": "src/utils/random_utils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9800a867d1fbbcd7d49194520bb8128ce2", "path": "src/dsp/rescaler.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9858d83fed1fc0db15fc7949493b3c9ee2", "path": "src/dsp/rescaler_mips32.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98270ecfb68bdd232b25d118797651c32c", "path": "src/dsp/rescaler_mips_dsp_r2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e987b5c2a3cf1dff55684bc7795745dacf4", "path": "src/dsp/rescaler_msa.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98ee23c5814942d3399dabc2ac7ef80983", "path": "src/dsp/rescaler_neon.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9829b17b34e6516ad2b29df8103d82a081", "path": "src/dsp/rescaler_sse2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9800bd1b8bb92a0846cd0454dc644628bb", "path": "src/utils/rescaler_utils.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981150553598ddf0a5165d193b980cc39e", "path": "src/utils/rescaler_utils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e983c7ebf3f6fe2eec74913eb7508dc9842", "path": "src/dsp/ssim.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9893421af2f5993c9ff88b310ee7fb42ae", "path": "src/dsp/ssim_sse2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9802e7e33fddecf94282d562ad405afcc6", "path": "src/enc/syntax_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e985b7ed4a4fe353e4cf6c74cca7d63dce9", "path": "src/utils/thread_utils.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985d9b79d04107ae9313cc0c3691e5f3e6", "path": "src/utils/thread_utils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98c0ade6a00ad31e2839a3d825810936bf", "path": "src/enc/token_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e983e805342477806db6eb8ed92db346fb2", "path": "src/dec/tree_dec.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98f3c39d251e0e4634cc995fd90f573b96", "path": "src/enc/tree_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9854c9f15b28feef97d6a7d5280bb69ecd", "path": "src/webp/types.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98f14efed1cabec602488a02d2cf52974d", "path": "src/dsp/upsampling.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9849b7bc05991a09701298832e09803a47", "path": "src/dsp/upsampling_mips_dsp_r2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e989bc41e6580ceae005449e3f58773f2ed", "path": "src/dsp/upsampling_msa.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98408ee587c78666d137e987ae40d610ad", "path": "src/dsp/upsampling_neon.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e983d54f25dc92ce416d2ce23052f847b92", "path": "src/dsp/upsampling_sse2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e983e6aa010bdc09ffe1d28679fd1b11528", "path": "src/dsp/upsampling_sse41.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98c91fc06d4be77b109671515e3ba661ba", "path": "src/utils/utils.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989c49ab6d58dc446fb6b4fa29b053973a", "path": "src/utils/utils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e983bad5544ecc1e919ed54a69a183e28de", "path": "src/dec/vp8_dec.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9834c2e54b614285beb73774c5c6804b22", "path": "src/dec/vp8_dec.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98062e12b13ba5f24f1539eb208f52ea33", "path": "src/dec/vp8i_dec.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98de13cd6529402e9e13e43f41505e425a", "path": "src/enc/vp8i_enc.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98163622d15239bfae9d3f584c02a9a0b3", "path": "src/dec/vp8l_dec.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98ee777c266d92af97d532478feef2f5ab", "path": "src/enc/vp8l_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9834287f6cd6084aaf13aee3d7170c77ab", "path": "src/dec/vp8li_dec.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9881a1d8eeef440f60f4f77626d8722dd9", "path": "src/enc/vp8li_enc.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98912b49f8d2bf52d6551a96e708a05e14", "path": "src/dec/webp_dec.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98edcd886a01aa2c114767aea89e769b6c", "path": "src/enc/webp_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989e253ea933a73e78e2852aebf650825b", "path": "src/dec/webpi_dec.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e981f2c2fe26c0c55ee1628ef0756b465cf", "path": "src/dsp/yuv.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983ed7a1bb27045cc00748368cce3ef6f5", "path": "src/dsp/yuv.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98a65fcc2ab64655b5af2ffe45df80f342", "path": "src/dsp/yuv_mips32.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e980c1a5f591224303784b3900673db753e", "path": "src/dsp/yuv_mips_dsp_r2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98e779ed428fa3f986bdb11247507d5bb8", "path": "src/dsp/yuv_neon.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9832c5198ff4c150e5323719063118d4f2", "path": "src/dsp/yuv_sse2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98d5cbf183e011bb994b96974d31356cd5", "path": "src/dsp/yuv_sse41.c", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f4e9fc50f501bcec33e51b0255f3c1dc", "name": "webp", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9892b419557cf9c104069f460b42829d89", "name": "libwebp", "path": "libwebp", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9816d38b7789836dda68a85e7b484bf14c", "path": "Mantle/include/Mantle.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983ede4fa07836d45209b43cc895758076", "path": "Mantle/include/MTLJSONAdapter.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98db57d20fb2884c6883c1d204bf982776", "path": "Mantle/MTLJSONAdapter.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982a3e6ec6dc8e63f50859d4eff1f27330", "path": "Mantle/include/MTLModel.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985ea26526bf608190d94b403002c90c7c", "path": "Mantle/MTLModel.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985bc98390571d7dd8f638b0d6cc75f757", "path": "Mantle/include/MTLModel+NSCoding.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981019f5b51ec3b4a402ec12ad4204c707", "path": "Mantle/MTLModel+NSCoding.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98778fc83e0475caf816cfbb24b17ff109", "path": "Mantle/MTLReflection.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98436fc1374aecec14e5e11789a70b6119", "path": "Mantle/MTLReflection.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982619acdc1318b0ec1fd9a87ddc857073", "path": "Mantle/include/MTLTransformerErrorHandling.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986107fe77d1cce02b52d33ff2fa2030af", "path": "Mantle/MTLTransformerErrorHandling.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9851f2f8c1bc5db99dcad85dfa94097c0b", "path": "Mantle/include/MTLValueTransformer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988c85fe9685c894bc6635a4a6a50f27d6", "path": "Mantle/MTLValueTransformer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9848c0c4a57cbe1bed6f84662e1fb230dd", "path": "Mantle/include/NSArray+MTLManipulationAdditions.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98996ba5ab158eaa83cd96dd11d9b5746e", "path": "Mantle/NSArray+MTLManipulationAdditions.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98da9be424f3c534efb736e01a29bb552e", "path": "Mantle/NSDictionary+MTLJSONKeyPath.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98531e46fe19dd36c867f67eef725a76a7", "path": "Mantle/NSDictionary+MTLJSONKeyPath.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f2acc914c5de2ad3fb51a5cba7cdbb93", "path": "Mantle/include/NSDictionary+MTLManipulationAdditions.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b8b7097fda4a9816459bfe14426738d8", "path": "Mantle/NSDictionary+MTLManipulationAdditions.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d8b14cda1684e1a29539a5fcb1c83abb", "path": "Mantle/include/NSDictionary+MTLMappingAdditions.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98919fc390a930bf73d455878411e3a398", "path": "Mantle/NSDictionary+MTLMappingAdditions.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985084cd1970e345762e67b478871782c0", "path": "Mantle/NSError+MTLModelException.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9805e750604e8deb204d42f0884c5e77c8", "path": "Mantle/NSError+MTLModelException.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a3578c91550f2180959032d72a37c7d9", "path": "Mantle/include/NSObject+MTLComparisonAdditions.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9861cdfa6a8a6e761284b07e007574daa6", "path": "Mantle/NSObject+MTLComparisonAdditions.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9870fb13a07d28a5d5fcb0c5d5de5e4a34", "path": "Mantle/include/NSValueTransformer+MTLInversionAdditions.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9805a6380451e12cb3cfbd03db56668a85", "path": "Mantle/NSValueTransformer+MTLInversionAdditions.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9828c9558cca9660e9fb13283f2f44a675", "path": "Mantle/include/NSValueTransformer+MTLPredefinedTransformerAdditions.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9856dddafff640c4377cf5712a0a436753", "path": "Mantle/NSValueTransformer+MTLPredefinedTransformerAdditions.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c69e1b8d791654efe53d53d1406635e5", "path": "Mantle/extobjc/include/MTLEXTKeyPathCoding.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986858da4a887ec3b77568a85cc17c1f34", "path": "Mantle/extobjc/include/MTLEXTRuntimeExtensions.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98698d046c3ad5e8117fa813f5188c9620", "path": "Mantle/extobjc/MTLEXTRuntimeExtensions.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987bdaa85152c116422fdc805916ff23de", "path": "Mantle/extobjc/include/MTLEXTScope.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9826d2963fd9efe4777a44e8e4e62fe76b", "path": "Mantle/extobjc/MTLEXTScope.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988867d8f56a5778288ba11e31197b8f31", "path": "Mantle/extobjc/include/MTLMetamacros.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98dd7eed2beccd39c475d6478850bc19f2", "name": "extobjc", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9832f7af8939ec15fd7a7cc348e6ec7302", "path": "Mantle.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982348fd0081e36fadca51e0a947840b6a", "path": "Mantle-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98bbf945fff207f045ca5d28e9b70ef259", "path": "Mantle-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980fc56ef25b69bca94073a6e53be71b55", "path": "Mantle-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9890ee82a1c1d65ca87d9b2d20582ad237", "path": "Mantle-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e988570e099a2aae5df8c8db29e49c04851", "path": "Mantle.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98eb4640b1fdc6b97846e69367bad5ab5f", "path": "Mantle.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98649d3483f0e53c0569bfb3e834f2a43c", "name": "Support Files", "path": "../Target Support Files/Mantle", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9830d06d7452742eb9882d088204b73393", "name": "Mantle", "path": "Mantle", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9857eab307d77ebbc20a1874ef3d02060d", "path": "Sources/ScreenProtectorKit/ScreenProtectorKit.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983cf393b38e1b6cec4fe073f448eb09ac", "path": "Sources/ScreenProtectorKit/UIColor+Extension.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98c45e03d9de81d0dacea64b850a4d920b", "path": "ScreenProtectorKit.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c15bd0b077edff19cd48cb2086f49327", "path": "ScreenProtectorKit-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9873b6c239acc326bbd75165d5c42f4692", "path": "ScreenProtectorKit-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9854e759b6378e972cacb929f0e7ab785f", "path": "ScreenProtectorKit-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98da3889a55637f3088b837aada42f8fe4", "path": "ScreenProtectorKit-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98a1cc324f1f59fcc5ef03fe254f0e4744", "path": "ScreenProtectorKit.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98f34e6ae9cc64d914375373f0d46db1db", "path": "ScreenProtectorKit.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9829c31aebf306534f6abd2319881dd3a6", "name": "Support Files", "path": "../Target Support Files/ScreenProtectorKit", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f0d30201e0b70bf640827aea49b58b13", "name": "ScreenProtectorKit", "path": "ScreenProtectorKit", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98405e9691c669f97aa98da51b1445b2e2", "path": "SDWebImage/Private/NSBezierPath+SDRoundedCorners.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988f4c43997701cbf8b4dcb8933ccf9a18", "path": "SDWebImage/Private/NSBezierPath+SDRoundedCorners.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9872b83ea2ac0a78f3f756b3e657e5aad1", "path": "SDWebImage/Core/NSButton+WebCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98bf63a0c1e0601b814acd13184cdb7471", "path": "SDWebImage/Core/NSButton+WebCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987ec281eaa704866103eddd1d27dcddfe", "path": "SDWebImage/Core/NSData+ImageContentType.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98074e63963178b1b6d80cbe3886988d38", "path": "SDWebImage/Core/NSData+ImageContentType.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983ae67dabb9bfd37eb53851f872bfb804", "path": "SDWebImage/Core/NSImage+Compatibility.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e1562a21900d56563ff94862c9707177", "path": "SDWebImage/Core/NSImage+Compatibility.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982fdea9345fbf8791a0179f91af784632", "path": "SDWebImage/Core/SDAnimatedImage.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988af21b550b35c50a7f24794a68c80ae4", "path": "SDWebImage/Core/SDAnimatedImage.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98db43323d726666d9a4814cc537ace112", "path": "SDWebImage/Core/SDAnimatedImagePlayer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9873aff128e05d11a392217d322b6e33fa", "path": "SDWebImage/Core/SDAnimatedImagePlayer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b4d720ac963e7d54e97123397f506a55", "path": "SDWebImage/Core/SDAnimatedImageRep.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98159f0409c589b74a85d6ac6d571ccee3", "path": "SDWebImage/Core/SDAnimatedImageRep.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98784dcf4782ea243c55d019d873b4418c", "path": "SDWebImage/Core/SDAnimatedImageView.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989388f49e950215b1eaf8a008af4ba2b0", "path": "SDWebImage/Core/SDAnimatedImageView.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c414ffbdf275dd9490b39274459ea11c", "path": "SDWebImage/Core/SDAnimatedImageView+WebCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d15608f520aa537f487b7c0bff05db9c", "path": "SDWebImage/Core/SDAnimatedImageView+WebCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9808b0b651f7d8979aac3c75d666faf52f", "path": "SDWebImage/Private/SDAssociatedObject.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9814a3b6b7f8adbcb75db6324c6384b9f3", "path": "SDWebImage/Private/SDAssociatedObject.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ce6b25aa8f2cb4696c36aa0100a52a16", "path": "SDWebImage/Private/SDAsyncBlockOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9809ab4ae01c4574b26e25247eb8b853a2", "path": "SDWebImage/Private/SDAsyncBlockOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fbb8dcf9426ae4557ea0e0d7676e5d6f", "path": "SDWebImage/Core/SDCallbackQueue.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98cac452df393edbdc85d74c105fe2e3eb", "path": "SDWebImage/Core/SDCallbackQueue.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98eeaad52e8a62af59d9ddd41e23fafe25", "path": "SDWebImage/Private/SDDeviceHelper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988caaaae00c4abe4987b38331458489b6", "path": "SDWebImage/Private/SDDeviceHelper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98302a8f7987a9338cccc3b2a51d9691aa", "path": "SDWebImage/Core/SDDiskCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98156b84c081d7d53d7ea0a122f670e2a0", "path": "SDWebImage/Core/SDDiskCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9810d4b4cdba2ca7ee3d830f21b0f49868", "path": "SDWebImage/Private/SDDisplayLink.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98451906668a441ee48aa71f6d4752a380", "path": "SDWebImage/Private/SDDisplayLink.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987fbd536c549f7a8994036ef25ac32275", "path": "SDWebImage/Private/SDFileAttributeHelper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981630367597398fc7878f68fbc74bee2c", "path": "SDWebImage/Private/SDFileAttributeHelper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d9f41feb3819c5248bcf90c6a47a1ab7", "path": "SDWebImage/Core/SDGraphicsImageRenderer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9801fccaf3d9fd718005803b8b6447499b", "path": "SDWebImage/Core/SDGraphicsImageRenderer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980d2e43740059188a1ec4f133bdcd766d", "path": "SDWebImage/Core/SDImageAPNGCoder.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fef93c35f417837e69dd00bdd28dc964", "path": "SDWebImage/Core/SDImageAPNGCoder.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984c2d9ffbf29b5b08c12ba04b75559dc6", "path": "SDWebImage/Private/SDImageAssetManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98aa090920d956b79fc8d38fe918f17392", "path": "SDWebImage/Private/SDImageAssetManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e1bb31cbd9f987acbf9be9f1fee905af", "path": "SDWebImage/Core/SDImageAWebPCoder.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9896af4840fac8bd2668618615cf70e5d9", "path": "SDWebImage/Core/SDImageAWebPCoder.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986010d8d92535ba2ebdbf5c800a1e9528", "path": "SDWebImage/Core/SDImageCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98256c620224f5d7e56179d2c25aaa4363", "path": "SDWebImage/Core/SDImageCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b3d664869782a813bd796cd081f33297", "path": "SDWebImage/Core/SDImageCacheConfig.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e63005f2504bc1eb9c01565128c3ba6a", "path": "SDWebImage/Core/SDImageCacheConfig.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ce9588551153adeab24ef83c069a6983", "path": "SDWebImage/Core/SDImageCacheDefine.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985bd517b987dd1ad1a93fe51e8cb81d6b", "path": "SDWebImage/Core/SDImageCacheDefine.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ec6fd1d5053bce1947d41f2fe1680301", "path": "SDWebImage/Core/SDImageCachesManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982efcff826da8907dbb1e4b5e10e0843c", "path": "SDWebImage/Core/SDImageCachesManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986390c56a16cf9e87c0247b0776516ce3", "path": "SDWebImage/Private/SDImageCachesManagerOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a7684a59440d4e3236b214da9fa80110", "path": "SDWebImage/Private/SDImageCachesManagerOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986e8f2cba43963b2657a4a7f42ef92a5d", "path": "SDWebImage/Core/SDImageCoder.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985c025f5a69894747a100f08335c015ff", "path": "SDWebImage/Core/SDImageCoder.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e74ea3058025e40f548cfcba42e6d4ca", "path": "SDWebImage/Core/SDImageCoderHelper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98bc7dbe0e6b5fc4eb46b589846cdd2a62", "path": "SDWebImage/Core/SDImageCoderHelper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984c29ea5f2b124f3f183e5c0ce115018d", "path": "SDWebImage/Core/SDImageCodersManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9887f52a7a53805039c126308abe790a58", "path": "SDWebImage/Core/SDImageCodersManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9823e21f8dc8403eb5716d67da96ecf107", "path": "SDWebImage/Core/SDImageFrame.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98544c9434ba69c4b506219b28c99c8703", "path": "SDWebImage/Core/SDImageFrame.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989f0cc7e0bda0e816110409bf7d21a5ba", "path": "SDWebImage/Private/SDImageFramePool.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98eba1cf6702b78f8f380856675373a9cf", "path": "SDWebImage/Private/SDImageFramePool.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b6bcacd79636e582785aa3a37b6a4dc0", "path": "SDWebImage/Core/SDImageGIFCoder.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98087ed5712399b16c1d94acb46365b436", "path": "SDWebImage/Core/SDImageGIFCoder.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987d45ca6e6a2be17a313eec43394a3d88", "path": "SDWebImage/Core/SDImageGraphics.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986aa30bb287e4d129ceed6254bd7aad68", "path": "SDWebImage/Core/SDImageGraphics.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982f8ee5601f330140067b7b2bc78ffc0c", "path": "SDWebImage/Core/SDImageHEICCoder.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9815ba01887a3bcae3496afa910d021bc2", "path": "SDWebImage/Core/SDImageHEICCoder.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9849982605fa8643fe3d0efca7810f4de6", "path": "SDWebImage/Core/SDImageIOAnimatedCoder.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98eaee0a54a9ea47773f96709968dbd314", "path": "SDWebImage/Core/SDImageIOAnimatedCoder.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98aa7e7729bf0dac878c1e79de9db16d0d", "path": "SDWebImage/Private/SDImageIOAnimatedCoderInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9850534c18a8145daa27e94d78491ea692", "path": "SDWebImage/Core/SDImageIOCoder.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981480e6471c0a4b5f8b1241468c613e63", "path": "SDWebImage/Core/SDImageIOCoder.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a5c6a3e5aae2857564e5909b2c2f7ba2", "path": "SDWebImage/Core/SDImageLoader.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9855a69dc88e5ad1e50b0b48ab0ec6df62", "path": "SDWebImage/Core/SDImageLoader.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a9c9b971fd52dfdf3d31a7b4bf4202b6", "path": "SDWebImage/Core/SDImageLoadersManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9853de967aa2d194e557a9bbe18e7eb7c9", "path": "SDWebImage/Core/SDImageLoadersManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d3a0b016878fe0938aac832f7db5d628", "path": "SDWebImage/Core/SDImageTransformer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985008286b5513c00f8ce065cd2a11da63", "path": "SDWebImage/Core/SDImageTransformer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98604f457defa0d2abf101261365d7646d", "path": "SDWebImage/Private/SDInternalMacros.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b3d2c53308747c4fe182f238435bd4a2", "path": "SDWebImage/Private/SDInternalMacros.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f815b96e0bc145406bcf2c217be6317c", "path": "SDWebImage/Core/SDMemoryCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98639b04da63472a73369eee672e5c54f0", "path": "SDWebImage/Core/SDMemoryCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989df920877047174582d98e7fa177d7a8", "path": "SDWebImage/Private/SDmetamacros.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98be4724ca27b235fcba93eb3a89fa8f24", "path": "SDWebImage/Private/SDWeakProxy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9861c2a3a64ea408e20a8a59b789938997", "path": "SDWebImage/Private/SDWeakProxy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9882e569209b8e636f1649646068f2edf3", "path": "WebImage/SDWebImage.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983bedc834be6257b44300db983b65f367", "path": "SDWebImage/Core/SDWebImageCacheKeyFilter.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f4c8f74bc14a63239f023068a92c8c84", "path": "SDWebImage/Core/SDWebImageCacheKeyFilter.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988257cd0725598c0a07f0b9c2a712f8da", "path": "SDWebImage/Core/SDWebImageCacheSerializer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98dc93ad580cfae55081c52d5e17e65c5b", "path": "SDWebImage/Core/SDWebImageCacheSerializer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98300f31a0c9e550bf4bcff18e2c49af22", "path": "SDWebImage/Core/SDWebImageCompat.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98da9dfb9de0af052e8044044a56a50e50", "path": "SDWebImage/Core/SDWebImageCompat.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9812ade32ee49643e9105782a6f621b20e", "path": "SDWebImage/Core/SDWebImageDefine.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9899eda2b4d31489fd47675ba4df23ecce", "path": "SDWebImage/Core/SDWebImageDefine.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98367a108e069e8f9166348cf35a92798f", "path": "SDWebImage/Core/SDWebImageDownloader.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980c76c1737f7be8240da84899595a0328", "path": "SDWebImage/Core/SDWebImageDownloader.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a76c70950c3d3c31279c772b6b75cc22", "path": "SDWebImage/Core/SDWebImageDownloaderConfig.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f743a71fab8360e40f4aca6d10ef873a", "path": "SDWebImage/Core/SDWebImageDownloaderConfig.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984664aab5cc93130fde06d9452fb5a54e", "path": "SDWebImage/Core/SDWebImageDownloaderDecryptor.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c7e00d7f6181349b5b7e9859da06cd86", "path": "SDWebImage/Core/SDWebImageDownloaderDecryptor.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c14193f189d8f21b951f0a00ac71b436", "path": "SDWebImage/Core/SDWebImageDownloaderOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9848f00cdae510a9b2a861a6fdf8e753e5", "path": "SDWebImage/Core/SDWebImageDownloaderOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98eb11b4fbe8f4bc69693411b4ec0c5040", "path": "SDWebImage/Core/SDWebImageDownloaderRequestModifier.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98454d8c60969da4fb6f87a26eaef9d756", "path": "SDWebImage/Core/SDWebImageDownloaderRequestModifier.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98487c021f19b1f3982c2666be07e0b582", "path": "SDWebImage/Core/SDWebImageDownloaderResponseModifier.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e01138b8d9ac2671ec2439165722061d", "path": "SDWebImage/Core/SDWebImageDownloaderResponseModifier.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fe598c839cfa49d2de6db742a03a7399", "path": "SDWebImage/Core/SDWebImageError.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ff165e14068496ce674e1efcc66b6b25", "path": "SDWebImage/Core/SDWebImageError.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988ad52423c9c93020b0b0c5af16653d0d", "path": "SDWebImage/Core/SDWebImageIndicator.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9818e6f80305d93b261f4e61a238c8612e", "path": "SDWebImage/Core/SDWebImageIndicator.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a8e541a8f25d8512f729dd3a999d951e", "path": "SDWebImage/Core/SDWebImageManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987fe6554f40f9963c9768300e4e3fbc57", "path": "SDWebImage/Core/SDWebImageManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bcc6f66b9d5f93d6756c6cd196229236", "path": "SDWebImage/Core/SDWebImageOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f6cc991445dfeece9d4f1fd61b3b3866", "path": "SDWebImage/Core/SDWebImageOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980775019a65c97732ebdafec59c1396e1", "path": "SDWebImage/Core/SDWebImageOptionsProcessor.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983e56c4d08151f485d910dc963bdd3117", "path": "SDWebImage/Core/SDWebImageOptionsProcessor.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98df30a9590dd0465204ba7d3b53fc66b8", "path": "SDWebImage/Core/SDWebImagePrefetcher.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981b6a4ea679e70e22ef1c1402ce1e3b3f", "path": "SDWebImage/Core/SDWebImagePrefetcher.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ba7adb03036a6500d10107b715159cec", "path": "SDWebImage/Core/SDWebImageTransition.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a879f8ac8617999fec15b6d86a80a90e", "path": "SDWebImage/Core/SDWebImageTransition.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98567f335422af0bca0d3592a2d049f962", "path": "SDWebImage/Private/SDWebImageTransitionInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b457186333c59c9295e6fa4908625480", "path": "SDWebImage/Core/UIButton+WebCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9821298bf5d0c335397139910cb028a63c", "path": "SDWebImage/Core/UIButton+WebCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9827b7357c7507bb845544d62881ba70cb", "path": "SDWebImage/Private/UIColor+SDHexString.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d0517adabd612ac5dd588539b1dabf55", "path": "SDWebImage/Private/UIColor+SDHexString.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985edcaf1db74b2d62f8e9904b808e4c6d", "path": "SDWebImage/Core/UIImage+ExtendedCacheData.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981d5c5a01690584b1b000c8ba749e83cf", "path": "SDWebImage/Core/UIImage+ExtendedCacheData.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98965eb871052821bf989181c119bd20bc", "path": "SDWebImage/Core/UIImage+ForceDecode.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987e0c24a14db159a804587d3105e30e11", "path": "SDWebImage/Core/UIImage+ForceDecode.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9820a2db90a6d8ce45012afef6168cda79", "path": "SDWebImage/Core/UIImage+GIF.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98218250e061ebf90b6b12753aba777414", "path": "SDWebImage/Core/UIImage+GIF.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ab86e9c1171eef82cce3f211033f2da6", "path": "SDWebImage/Core/UIImage+MemoryCacheCost.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98bfcfa2cae2c8e179976117cae3f37c7e", "path": "SDWebImage/Core/UIImage+MemoryCacheCost.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981446917dd75c0c63313e6c53fef6a0f7", "path": "SDWebImage/Core/UIImage+Metadata.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984b95d8f275161f166f8081edac691aa1", "path": "SDWebImage/Core/UIImage+Metadata.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c2ef988dc78fbade37fed30d66c1db69", "path": "SDWebImage/Core/UIImage+MultiFormat.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a3f49dc25bc173eb8950541562bc250f", "path": "SDWebImage/Core/UIImage+MultiFormat.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987839ae9fc0ff7eee8d00c2ea99f97ed1", "path": "SDWebImage/Core/UIImage+Transform.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ad3b3431891c3f89ce845c89c2e146f1", "path": "SDWebImage/Core/UIImage+Transform.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98065f396255249655d023aec74223dd3a", "path": "SDWebImage/Core/UIImageView+HighlightedWebCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b336ca9a1d40cd3ad56977963bab862f", "path": "SDWebImage/Core/UIImageView+HighlightedWebCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c7a338b84f1cc7fecdf91bdfc5df1303", "path": "SDWebImage/Core/UIImageView+WebCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9817d553fbdbc2de4073e6595d9f607b51", "path": "SDWebImage/Core/UIImageView+WebCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988c81b96fa09832620ff3df45ab252f9a", "path": "SDWebImage/Core/UIView+WebCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98dd2ed6353c0cbdbaee875c03b6ea5247", "path": "SDWebImage/Core/UIView+WebCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9882c48f0043488a1942230ecad454a94a", "path": "SDWebImage/Core/UIView+WebCacheOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b7e98a676f1b7e525c469b68b6c37ca2", "path": "SDWebImage/Core/UIView+WebCacheOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9827d07ef82cc9509cc1eb84b9a9061c07", "path": "SDWebImage/Core/UIView+WebCacheState.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981cac937f800dd6ce69a0a542d67aa424", "path": "SDWebImage/Core/UIView+WebCacheState.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9891f58aabf12acbbc1d40fe4bf7b941e7", "path": "WebImage/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98cbbfd12f6c4d2f8713485e4b508b9085", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dbc676dbd0a9aea86f74aff2fb1c8706", "name": "Core", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e989c47fdbf3de3dc42bbd7b804648d2f2c", "path": "ResourceBundle-SDWebImage-SDWebImage-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e986aca93620483eb1eec1e0254374aa39e", "path": "SDWebImage.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981f052c1dbed59d55380aa73280f1addf", "path": "SDWebImage-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e982f475b4a6d6b00e8f5aa8e76a1f12b2e", "path": "SDWebImage-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b02f726689e611a6df118c60bde4a7a0", "path": "SDWebImage-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b561ae2ffa969336928cfd5a3c888218", "path": "SDWebImage-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98601515524ba30f8dc97499c9c56351ad", "path": "SDWebImage.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e984da2dac88a2b01b81798f8e3dc12f766", "path": "SDWebImage.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98a6a69fc18bfc8235bee0aef35858f211", "name": "Support Files", "path": "../Target Support Files/SDWebImage", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ef26eff8102bb10ab0cfb6319fdc940e", "name": "SDWebImage", "path": "SDWebImage", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986bfa3a25318c6ce6a182d0a421f91c0d", "path": "SDWebImageWebPCoder/Classes/SDImageWebPCoder.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9899ec0df01c36c7e941dcded94f1e2567", "path": "SDWebImageWebPCoder/Classes/SDImageWebPCoder.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980d81cb3d38b1db066cc6cabd06094301", "path": "SDWebImageWebPCoder/Private/SDInternalMacros.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e2934812bce4186257e8c7bd086526e3", "path": "SDWebImageWebPCoder/Private/SDmetamacros.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981ef0cacd362d2bcd59208cff15935e5b", "path": "SDWebImageWebPCoder/Module/SDWebImageWebPCoder.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983f0b00cabf58d7a0aa31d91329d78f27", "path": "SDWebImageWebPCoder/Classes/SDWebImageWebPCoderDefine.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9854dd9fe00dad4f48bd78a7f9fe8ad063", "path": "SDWebImageWebPCoder/Classes/SDWebImageWebPCoderDefine.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9863d77ae72e8bef466f3fb2e2888d85ec", "path": "SDWebImageWebPCoder/Classes/UIImage+WebP.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a9725a5c123bc994d15a5be534b6ec41", "path": "SDWebImageWebPCoder/Classes/UIImage+WebP.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98c5266ce12cbe6ce427d3008b1cdaab47", "path": "SDWebImageWebPCoder.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98bf3e2ac52babc3e9e94c01b7604f9614", "path": "SDWebImageWebPCoder-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9828a93c7ced11817ecab4085df95e581a", "path": "SDWebImageWebPCoder-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989800267c61e1f53ade69356edc9358c1", "path": "SDWebImageWebPCoder-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98fee35ae65407c51a11c24d37932a5508", "path": "SDWebImageWebPCoder.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9803c4fb7cd037f87e84c87b09e89cf703", "path": "SDWebImageWebPCoder.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e987a083216e9aed0e38203c24799dd5d6e", "name": "Support Files", "path": "../Target Support Files/SDWebImageWebPCoder", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f2c737606c6dfd5b13c077bf0907c886", "name": "SDWebImageWebPCoder", "path": "SDWebImageWebPCoder", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98857cbee52286306d590eaca6ac0342c6", "name": "Pods", "path": "", "sourceTree": "<group>", "type": "group"}, {"guid": "bfdfe7dc352907fc980b868725387e98123d92794c13134e70ec5e0a18312d82", "name": "Products", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98d85490c13bb594476aa9be285597497d", "path": "Pods-Runner.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98c50a2eb9fb28cebb3540daef5d4a8334", "path": "Pods-Runner-acknowledgements.markdown", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e987b0bdfb96c434b1bdaf98ff08db5d964", "path": "Pods-Runner-acknowledgements.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989c24b47fd1a04f7c3870243d256eb710", "path": "Pods-Runner-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.sh", "guid": "bfdfe7dc352907fc980b868725387e986b56855213c29113cc17d2b495b4605b", "path": "Pods-Runner-frameworks.sh", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98850ee204ad70211ee248c6855349a5f9", "path": "Pods-Runner-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f67184b23266d4586865ab49f1bd9d8e", "path": "Pods-Runner-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e986175ec003efd0925b0e80b27c1a333bb", "path": "Pods-Runner.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e981c04a92782605bedf8b5bd015c8dc01a", "path": "Pods-Runner.profile.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98ab5e5b11fcf9a2f9f4c2bf61b3a5e465", "path": "Pods-Runner.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98dc2407b6e3245e78631c8d5833a16aaa", "name": "Pods-Runner", "path": "Target Support Files/Pods-Runner", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e983aadeb6c0efc55aa61d4a193c33d1a65", "path": "Pods-RunnerTests.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98a634232c699d5ed3646d3f024c937ffa", "path": "Pods-RunnerTests-acknowledgements.markdown", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e989938b906e3cb2707a2afa9a39150a604", "path": "Pods-RunnerTests-acknowledgements.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988d01c1d667722a31ce8e51428338963f", "path": "Pods-RunnerTests-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98a85576fdb8cb73c7cd4dd5902a45a27b", "path": "Pods-RunnerTests-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f448039d0a832e98acdd3ffd87da1731", "path": "Pods-RunnerTests-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98074a9b441078beef16a37aae33ee2900", "path": "Pods-RunnerTests.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e987bd2a5c12d5a72dad789e04e26dc5a25", "path": "Pods-RunnerTests.profile.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98fc46d149385d28a69f8f8bc860e81763", "path": "Pods-RunnerTests.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98d5836cf4d97a0c9c99eca09bf2351047", "name": "Pods-RunnerTests", "path": "Target Support Files/Pods-RunnerTests", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f3d357a58233f32f97cf5aa060ebc8be", "name": "Targets Support Files", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98677e601b37074db53aff90e47c8f96d1", "name": "Pods", "path": "", "sourceTree": "<group>", "type": "group"}, "guid": "bfdfe7dc352907fc980b868725387e98", "path": "/Users/<USER>/StudioProjects/noorbiology/ios/Pods/Pods.xcodeproj", "projectDirectory": "/Users/<USER>/StudioProjects/noorbiology/ios/Pods", "targets": ["TARGET@v11_hash=f1ceadb28bf91499da3c25540c30ab8a", "TARGET@v11_hash=c4735a59644b3e77ff140a79fe18fdf7", "TARGET@v11_hash=f5ad3b8d3818f7e4887fc56403f4d2ec", "TARGET@v11_hash=b3d66b768fe2909b0fad8822e04ddb8b", "TARGET@v11_hash=22d8df71c68f521772223fcc75d9a9e5", "TARGET@v11_hash=8df3c533aa3b3698c4c08dd7199e97c0", "TARGET@v11_hash=5893584fccfcc9af0afb55a3ee8a4e85", "TARGET@v11_hash=dc8fa61cfb28b51bc5abdf2f3c3492d0", "TARGET@v11_hash=9351b92bab07cf0974e68f47eccffcbb", "TARGET@v11_hash=3554c10702e87c3aecc4324c4cadf7c4", "TARGET@v11_hash=274f7c47cea181768d8b919631bd9e2e", "TARGET@v11_hash=73af634e1aa71beda94fd55d12822635", "TARGET@v11_hash=4258696cc74bebdec7c3ae3cb5f7bba2", "TARGET@v11_hash=ed51bcf9be1a10eccc21a42313a78e7a", "TARGET@v11_hash=720f7e97635d5efec864d3f3e8f5eeed", "TARGET@v11_hash=db020072129f12e2a7b9c2e7bb709d98", "TARGET@v11_hash=6ba8bf578306dadb11877689d25c22bd", "TARGET@v11_hash=c79c8648bf3e6eb2192161bc5d288b2e", "TARGET@v11_hash=4e7a2524ac56cf88f502e79f6cd98529", "TARGET@v11_hash=c6c7b94228f29a0cf16f16636bb0e2e3", "TARGET@v11_hash=ba34686f0743a9915f04746beb5f4830", "TARGET@v11_hash=de71db61a4f1892d54253733bb2196c4", "TARGET@v11_hash=0bec04ea9a867a4d32042c2aba47f317", "TARGET@v11_hash=b087a3fc874ce1b5551ac1b4110e72ef"]}