{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987a7077cf10d8b52fb1035348436af02b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980b0491debad13020a165ab8d8fb8e7a6", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d5b31d94a0dd62c84c2e605ff4015dd4", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d5d17fb1d91f0a4f64158124b05fa286", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d5b31d94a0dd62c84c2e605ff4015dd4", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d48975f249f96592ebac62cabeb71693", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983b08aca9a405fd2ac1408c5b60b82da8", "guid": "bfdfe7dc352907fc980b868725387e9883c10ad6045e2edb3b45c1c6c0413b92", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e983a4fdc38bf986b19d561fd705b949d72", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c60bbc6e9428ef0023030ad061d20c62", "guid": "bfdfe7dc352907fc980b868725387e9862651134a512e8cacd066e88486ee781"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1811f47ec8b0a3e7c16ee909e9e3ab4", "guid": "bfdfe7dc352907fc980b868725387e98e46a3ea7c8d8e3c4f58bd225b3909de3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a47be928a3c002a0673d446e37af3e5", "guid": "bfdfe7dc352907fc980b868725387e984c4817cc7a5d092968c19fcdb2b6459e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819c965cf76915ea6aec9c3257055dffc", "guid": "bfdfe7dc352907fc980b868725387e983c2e16d3e610a0d0c22405eccb293da5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985328ece09c4d3e48794a0288277950f7", "guid": "bfdfe7dc352907fc980b868725387e98782cf73af5809191cf4eba4b8215b32e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ace0a64cc3dfdd87c1d629ee95ea1f0a", "guid": "bfdfe7dc352907fc980b868725387e98b07a1f3502a251b0026ba5261d9892c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8d13d674b336211f99b6bc4ea230e2d", "guid": "bfdfe7dc352907fc980b868725387e98497a11d603ca182693f00eddf1f6ce9c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b2bf0554ce8427719c558d9dbaa9e887", "guid": "bfdfe7dc352907fc980b868725387e9880b1b38c7f5676aa119fde5cc259eab3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc72a3f843f9a1b7295f2b75f05ea7b1", "guid": "bfdfe7dc352907fc980b868725387e981dd43cb2063d9cd3afa56d7f66c4ba25"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a7545124a4e3af91c692d4d321a1573", "guid": "bfdfe7dc352907fc980b868725387e98a8fe11436d7a2f3eebe3f405b66dbdff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f278ab2c0a0ca06b6d7ecd6bae299bd2", "guid": "bfdfe7dc352907fc980b868725387e98e73df0f8362d1d943d6e1181b802b3a6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f73480c394dbb0efa271e52bf07c77ea", "guid": "bfdfe7dc352907fc980b868725387e9856b7b969cf7c20623183c368463a697b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98257acf164d59c47675a813882dfd029c", "guid": "bfdfe7dc352907fc980b868725387e98643821d4052111904f32bf5c442380e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b34b835e1fc5053463a58d3d09ed805a", "guid": "bfdfe7dc352907fc980b868725387e989868af65fb25ae54ae8e0f38bfacba0f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1d73464b9f9c1f9a06cd0de85568e1b", "guid": "bfdfe7dc352907fc980b868725387e98802d438d9ddbd77c9601785dbb7d394c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98170c1349269a8a4052f819538884c471", "guid": "bfdfe7dc352907fc980b868725387e9882aab3dea4052c1c5bf2cc71658ea085"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f154208db20283ddad0d0eb4b970a1c9", "guid": "bfdfe7dc352907fc980b868725387e98f742f3ae402ddf25d7f25ac225f31e7b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f4ccf4084b0f9c57799061b876da9d9e", "guid": "bfdfe7dc352907fc980b868725387e98e375d2c740dc56fa3928f7b4e5fd38b0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986fb8039c6555e022700a17a35a1f8cf2", "guid": "bfdfe7dc352907fc980b868725387e9875f982d1f53eafa32e61506e179df012"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f37b570f973ce10f309dcbe0f3a913c", "guid": "bfdfe7dc352907fc980b868725387e985712f61a919fd0546509996887119b3f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a73525390ce650719d9680126c09e3a7", "guid": "bfdfe7dc352907fc980b868725387e9838f1e89de5b780beff7f2ab0da85ab51"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d6ed8e51c52ea8297e46c3e90cafc67", "guid": "bfdfe7dc352907fc980b868725387e988154a78674a081fd2b1bae575d97ea21"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a685647dc68a69a132761c64f590c539", "guid": "bfdfe7dc352907fc980b868725387e98c20c76275a46289820cec64517207036"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988bc950b4b17a3b6490862521aa076b9d", "guid": "bfdfe7dc352907fc980b868725387e9888238703fd52b8f0555517d53f3bb6ef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd39b88d3ada50510cf467dcf6794b56", "guid": "bfdfe7dc352907fc980b868725387e989e413a6baba9c1bf849f2af5499ce929"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8da8f0c424d9555060bb8f99e623941", "guid": "bfdfe7dc352907fc980b868725387e98504bb4671717bcd3008dac00589d1ee5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de5141f5f2b92ebc5322f66946a48438", "guid": "bfdfe7dc352907fc980b868725387e9862ce2a6660e35ae0787ea311f2990ae4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98268b84883bfda5ab9f675b849bd16bb5", "guid": "bfdfe7dc352907fc980b868725387e983c0f40906c053a846f1dd431125e1d37"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856e74174d13b77ec6dc2503d4ac764cc", "guid": "bfdfe7dc352907fc980b868725387e985df657a9042e8c9e4cda282b86c5ff73"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba4b93cbd4461c459fea002ad80ad50b", "guid": "bfdfe7dc352907fc980b868725387e98bc3028553d295cec3f77a3aa86f31da6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883262d4af7098d1581cb6d71b4ce3dc0", "guid": "bfdfe7dc352907fc980b868725387e980532408a5977baceffd485c445e2786b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d3abb8a9d44fb23dd82696069c5afe5c", "guid": "bfdfe7dc352907fc980b868725387e9885b271a99769685c618f5c8b4755eb52"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f615a41b5f5934d9f5e62bf073794901", "guid": "bfdfe7dc352907fc980b868725387e98bfc24a8b5ffa1148bf938220aeb2446a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f419aad6544098d5fe70e411e211ccc", "guid": "bfdfe7dc352907fc980b868725387e981c4bd8d279e2891906d36301438a9802"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba26fcd13a03301ec5f5af4ec7da321b", "guid": "bfdfe7dc352907fc980b868725387e9832c964d6b6d3fe2b1dfcc12fa6a6c68f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888b9c161bba790f935f709754b206ec7", "guid": "bfdfe7dc352907fc980b868725387e98fb44c96e0c18e64269da26c34c9c3854"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98589e7af0ea39032142b118c6e67e4dde", "guid": "bfdfe7dc352907fc980b868725387e986000b6b5d65d9674e29a05e714e77645"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e794cdd11b37ebcaead6ee9920d4c90", "guid": "bfdfe7dc352907fc980b868725387e983b6d66cd85eaff4115b37894cd1909d7"}], "guid": "bfdfe7dc352907fc980b868725387e98579fbecb47bc990731107675682637c6", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987169fcd29c627f30e820b00793706b3f", "guid": "bfdfe7dc352907fc980b868725387e9817874d657b3f6cd3a4f0acf89676b8a6"}], "guid": "bfdfe7dc352907fc980b868725387e98e3e0578284590adbf8b73f59c73cb7a7", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98acb6d21f8b6048502a790d59f4fc84cb", "targetReference": "bfdfe7dc352907fc980b868725387e987c93e943aa0a38b5f6684beaf6b4a3a1"}], "guid": "bfdfe7dc352907fc980b868725387e98be611f6cbf3440ae2e558dafe4f7c459", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e987c93e943aa0a38b5f6684beaf6b4a3a1", "name": "webview_flutter_wkwebview-webview_flutter_wkwebview_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e988efdc4dd0ac29b43123295eca853f4ed", "name": "webview_flutter_wkwebview", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e980823710353e0487822d6da09bf8d6254", "name": "webview_flutter_wkwebview.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}