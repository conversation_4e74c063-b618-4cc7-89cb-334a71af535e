{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98fee35ae65407c51a11c24d37932a5508", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/SDWebImageWebPCoder/SDWebImageWebPCoder-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/SDWebImageWebPCoder/SDWebImageWebPCoder-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/SDWebImageWebPCoder/SDWebImageWebPCoder.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "SDWebImageWebPCoder", "PRODUCT_NAME": "SDWebImageWebPCoder", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980376258cf776abf8ce6cb4e71cf0e5c2", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9803c4fb7cd037f87e84c87b09e89cf703", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/SDWebImageWebPCoder/SDWebImageWebPCoder-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/SDWebImageWebPCoder/SDWebImageWebPCoder-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/SDWebImageWebPCoder/SDWebImageWebPCoder.modulemap", "PRODUCT_MODULE_NAME": "SDWebImageWebPCoder", "PRODUCT_NAME": "SDWebImageWebPCoder", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9882724d5ad0240f81686d533b9f06f575", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9803c4fb7cd037f87e84c87b09e89cf703", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/SDWebImageWebPCoder/SDWebImageWebPCoder-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/SDWebImageWebPCoder/SDWebImageWebPCoder-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/SDWebImageWebPCoder/SDWebImageWebPCoder.modulemap", "PRODUCT_MODULE_NAME": "SDWebImageWebPCoder", "PRODUCT_NAME": "SDWebImageWebPCoder", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9822f672670fb4d72497b3a2f5eeb8b914", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986bfa3a25318c6ce6a182d0a421f91c0d", "guid": "bfdfe7dc352907fc980b868725387e986c283334b0f78e3ce7f3995e776821e1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d81cb3d38b1db066cc6cabd06094301", "guid": "bfdfe7dc352907fc980b868725387e989118f7791c2917f8c155075204020844", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2934812bce4186257e8c7bd086526e3", "guid": "bfdfe7dc352907fc980b868725387e98edd755837502d44416c150d3b1850839", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ef0cacd362d2bcd59208cff15935e5b", "guid": "bfdfe7dc352907fc980b868725387e98c1c06d18c4cc9266c1b3785d6953d65e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f0b00cabf58d7a0aa31d91329d78f27", "guid": "bfdfe7dc352907fc980b868725387e98815d7e43900e1d49bfa3acf6cf130c8e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9863d77ae72e8bef466f3fb2e2888d85ec", "guid": "bfdfe7dc352907fc980b868725387e9890c9a0c5d198c44843a651b0fa6b7e3e", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98bcec3892e3fccf55522c44e10b4fb6d5", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9899ec0df01c36c7e941dcded94f1e2567", "guid": "bfdfe7dc352907fc980b868725387e98d19bd8b452c2d89908a483f66d228717"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf3e2ac52babc3e9e94c01b7604f9614", "guid": "bfdfe7dc352907fc980b868725387e983440d50d1770549113d8bc94de8edd25"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9854dd9fe00dad4f48bd78a7f9fe8ad063", "guid": "bfdfe7dc352907fc980b868725387e983d7985d437801894b0bbf80f5e1f8861"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9725a5c123bc994d15a5be534b6ec41", "guid": "bfdfe7dc352907fc980b868725387e98274fb2600683282693736ea4688ae907"}], "guid": "bfdfe7dc352907fc980b868725387e989bfdf2eedd58ad6f23c1b542e8c26b26", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9820753baf2bbe3b4d34284c7cdc004651", "guid": "bfdfe7dc352907fc980b868725387e98ef962ae0db04c0df1232eb871c3b9a10"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987169fcd29c627f30e820b00793706b3f", "guid": "bfdfe7dc352907fc980b868725387e98da5cb7aeacf8fec8deff4d47b3d76817"}], "guid": "bfdfe7dc352907fc980b868725387e98c3b08a8dbb89f44cf4421f62f39fd87e", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98a3b4fb766144d79e871a4ac6cdcde678", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98c46180aea4e87057640961e6db37df0d", "name": "SDWebImage"}, {"guid": "bfdfe7dc352907fc980b868725387e98459ecbbef4dbbe8a07a0c87bad0e0d1b", "name": "libwebp"}], "guid": "bfdfe7dc352907fc980b868725387e9837027570fe3a09bb9c7d0d2620332306", "name": "SDWebImageWebPCoder", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98a513e2d8d894b8762a51df43e9ec3f83", "name": "SDWebImageWebPCoder.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}