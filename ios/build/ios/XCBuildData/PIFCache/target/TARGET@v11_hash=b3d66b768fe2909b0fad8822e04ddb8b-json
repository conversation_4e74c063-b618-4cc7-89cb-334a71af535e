{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c888272dd958bff91da7750ce7d6f7f0", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_image_compress_common", "PRODUCT_NAME": "flutter_image_compress_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980fe6008bd0ea7d60a239f5cfb496f9fd", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d5a1291a0d9410e47cb96d45b4ab827b", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_image_compress_common", "PRODUCT_NAME": "flutter_image_compress_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984ac27f284fbf5fa4242b11e4c252631e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d5a1291a0d9410e47cb96d45b4ab827b", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_image_compress_common", "PRODUCT_NAME": "flutter_image_compress_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ec29c327167a123c1352766ac15557e9", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984a09544236a2cb34e2b6bd9f5553936d", "guid": "bfdfe7dc352907fc980b868725387e9840a9caeba178add7aa4dceb4c1bb75fe", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f42f9b84d4548f57830128aa7a7b4486", "guid": "bfdfe7dc352907fc980b868725387e98b4c61cc35fba40b4ce329132c3f61eca", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a143fdc0d966bb84d2404307931c52d", "guid": "bfdfe7dc352907fc980b868725387e9818da142fe82d8a78c7be9db931d371ba", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca525cbfd3077338368d792e2d8e5b1c", "guid": "bfdfe7dc352907fc980b868725387e98df995503faf86a7e536e8fd0813b9b8f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed22199190a3ac8a732ea7df73b52ac0", "guid": "bfdfe7dc352907fc980b868725387e984b4ae0c89031a24d1d71288e24a797ca", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98adae337420c3da7d87aa9808879ff1dd", "guid": "bfdfe7dc352907fc980b868725387e985f28519c628b2857ebb37b7b581178d4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f51508363d42ba84c36b3fde52ce0ac", "guid": "bfdfe7dc352907fc980b868725387e98d488e0a22f4c14aacdf4d40c2c9ddb44", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98633a8bf829df7337806de56e23d5bfdd", "guid": "bfdfe7dc352907fc980b868725387e98fe7273ad9aa9fdcb548c9c43dc93f8b4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c182e5a9bcdb44f5bfd7348ec8e58c1f", "guid": "bfdfe7dc352907fc980b868725387e98313d9e25b0bf5f3c97ad080d07ef3914", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa9091c2de31264e4161fafadc51370b", "guid": "bfdfe7dc352907fc980b868725387e989f407ff8592b35f500a6527a20317817", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985668eba3670c1e1a281af037f55f6815", "guid": "bfdfe7dc352907fc980b868725387e98e16efa990e491cc807ce6fc10625d15b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bfe0a97187f53ea1f1e9ed224ed46fbc", "guid": "bfdfe7dc352907fc980b868725387e98105db008f99641fadb0caf5f7506b3c4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840e8e93c043bf941a6b6d2f8931f6fd7", "guid": "bfdfe7dc352907fc980b868725387e98a6e4e157926dc87d2e4f2973b18d8bcc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9805d15c2a2892c075da7b2b4ab0e1c384", "guid": "bfdfe7dc352907fc980b868725387e98d1ddc3be1007fb9ee70d27ff32e03352", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d2c143823b98ab9ce26294d7ca13d0e", "guid": "bfdfe7dc352907fc980b868725387e980f430574f80b3377bafb28f408603313", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878a76e753969622ea39d953cfd1d2988", "guid": "bfdfe7dc352907fc980b868725387e9811c36f63ef52dcfc9629b13ac9cd2366", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee75dfbc2b6d178a46384b46cacecccb", "guid": "bfdfe7dc352907fc980b868725387e98fdcde1de031b3934e8ad535d6a77715a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d520ba33b097013c9705de9217a3b90", "guid": "bfdfe7dc352907fc980b868725387e9806312812ff309343658d470012c9949e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883d4db6f23a75b7b526bf55235efa6bf", "guid": "bfdfe7dc352907fc980b868725387e98fd547b1fbc8601ed045148719c9fcb31", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98168067fb6d1d6aca4901e092f533c267", "guid": "bfdfe7dc352907fc980b868725387e98b8107d30d41b046eabedd15b3e204207", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f81ee22b5012e2ca4f667106752e4dc", "guid": "bfdfe7dc352907fc980b868725387e985911bdce5650368ba78e000854a76edc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9896f46b31ba2bd6704a79ead0f51a833b", "guid": "bfdfe7dc352907fc980b868725387e9851879a7bd58d5db494aaffdae14944fc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a88d898c873d310658f5372e27f7a6c7", "guid": "bfdfe7dc352907fc980b868725387e986d5e368a2966e687c64f43486e1a14ce", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989fd6c7a5f7e2511010ba2fd996384bfa", "guid": "bfdfe7dc352907fc980b868725387e9892ad5fea3cd9806e3d88c69c494e5724", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9887cfbd107af9ddd09313f5634fa1e750", "guid": "bfdfe7dc352907fc980b868725387e982287d5443baa60a644e03eff995b0dbf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b072fddfb4e80b07491642084aef2df9", "guid": "bfdfe7dc352907fc980b868725387e984b7cfa5cae6af5dd957a2e5e2c72d887", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98086f0f35c0cd60837947c48c15c1fc13", "guid": "bfdfe7dc352907fc980b868725387e981354bfb9c1f8a7cd29cda84a949c2b50", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859b80a4feb5edc363e1045dac97518ed", "guid": "bfdfe7dc352907fc980b868725387e988ea9c6211d68a85a4fa5661a00b70d12", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98934cef9bf6ee848bfe3ec38d62197835", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986b7f6b7d49f109304660bef07545f750", "guid": "bfdfe7dc352907fc980b868725387e988601a4ee62b9ae3a9bcf8cd3d0ebe162"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983915365a6878ce493ab9ca0a36ddbc65", "guid": "bfdfe7dc352907fc980b868725387e98118fac16cfc7d0d72dbd7690930ee944"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c96b73e61e2b5ed17c99f35f8cd6450d", "guid": "bfdfe7dc352907fc980b868725387e988018d42a6cbf30bd26714051d751756f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986decf24521f40cdcddf9939d597f7af3", "guid": "bfdfe7dc352907fc980b868725387e9816670788bc7f1e372bfade4c15b03b8d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e07b97fd1f53c0b26ff532a10df6877", "guid": "bfdfe7dc352907fc980b868725387e98f8b7fbab77fdbedd35cf16a5f9b711bb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ce8152aada80bf317c1483dd6363c8b", "guid": "bfdfe7dc352907fc980b868725387e9865826dabae77a65c0f8e3f8837afaccf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809379d397fb18bf6367630a7c166b06e", "guid": "bfdfe7dc352907fc980b868725387e98b2432585b08dbc72dc97577aadf81be4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b1a98dfa60289d672e160e73c2e5d00", "guid": "bfdfe7dc352907fc980b868725387e98dea84ba5702c5f8810e62a4263d9ace3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fadaae4317be848ae29ce9ebf867e3e5", "guid": "bfdfe7dc352907fc980b868725387e9838e63e5a34be3fcb50907fe5f2f4afed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98923a4babb49b6980912ff27be8af1b92", "guid": "bfdfe7dc352907fc980b868725387e983efddb9b6fee895c6ea68388a011affc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b3bbb2c2b51cd0079a63d2477dcbc81", "guid": "bfdfe7dc352907fc980b868725387e989a76fbeb6bac5b3b1280afb834ae8aa5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864567d2d1969c4339c17c345757cdeba", "guid": "bfdfe7dc352907fc980b868725387e98ec6c6d0bf1756896b53daee803877721"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ec053d3d3e4ae6a20de30696c868ce3", "guid": "bfdfe7dc352907fc980b868725387e98e74fcc69d8150f502ccf1b3e2fc5485f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e35e3a7e6e2a429daa2bc7d35b1730f0", "guid": "bfdfe7dc352907fc980b868725387e98b821742c1f6f7ea4cd9c0b273279b76f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98689289ee05aa65270eb7ad30b99ac343", "guid": "bfdfe7dc352907fc980b868725387e982dc177db9f7d9ffb96479a0947cb42c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9812e186ecef156aefac54ab45506dc837", "guid": "bfdfe7dc352907fc980b868725387e9804e1e82783b73939a38d0ed8e1238d7d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98329a085be260c347348afc3d6c57f72a", "guid": "bfdfe7dc352907fc980b868725387e9809531441d9135d8d86d4ea73bb600633"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98352630cc3e55ff0361fcd746dd72a606", "guid": "bfdfe7dc352907fc980b868725387e986708b4f0dbacc243fb99a7eb06925479"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ce4491022b890553194e783b49a7689", "guid": "bfdfe7dc352907fc980b868725387e98670d25b97004c7a93f78975edd086e14"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba6400eef1ee7dfcbd34c00d52f17c39", "guid": "bfdfe7dc352907fc980b868725387e98f1a030d6e6eb0600473a3e08d860ea34"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f55bda1a70f195d7156c8c19e88d10ac", "guid": "bfdfe7dc352907fc980b868725387e98cc7934ae79503605f0492de7b872114f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ccf258e4d69b0f44720bbe02412e4a3", "guid": "bfdfe7dc352907fc980b868725387e98fbf436204971d95f60e7a593d7540c8a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98adbd42c713cfda1b8c7cf82d42492c8c", "guid": "bfdfe7dc352907fc980b868725387e9898aaa51c1a99ce45e80eb8f93420d71e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9875ce0c77c3b8503f61f94508e401d956", "guid": "bfdfe7dc352907fc980b868725387e98d0c2773f09c4cabb2cf0eebceee42fe5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98368ffe2a79fe847fe6a8f5c0418eb591", "guid": "bfdfe7dc352907fc980b868725387e98da7585a197ccba31a23ef6e300e35684"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb9a0327ff7eba4e4fd0a62a444d2561", "guid": "bfdfe7dc352907fc980b868725387e9877adb990468abb172a1f126cb52f85d0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98371b66b530c1924f19f7ab7e80dc6a11", "guid": "bfdfe7dc352907fc980b868725387e98cf068f37d84ba0e155873865791a4c16"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dcd8d71a643846a080a2d3e1d106f5e2", "guid": "bfdfe7dc352907fc980b868725387e982b47bac972003ddff7a9354e299d2485"}], "guid": "bfdfe7dc352907fc980b868725387e981f18c3bdd543f0383a05196ffefabc9e", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987169fcd29c627f30e820b00793706b3f", "guid": "bfdfe7dc352907fc980b868725387e9808188f8afd5c22436d8f270ee53ec170"}], "guid": "bfdfe7dc352907fc980b868725387e98f59cae5e753b0a31876694ce85c47944", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98ec476f90f1f6e616e836f5cbf5a8826f", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e982742fb9f3ddc293e43c65bab714408c6", "name": "Mantle"}, {"guid": "bfdfe7dc352907fc980b868725387e98c46180aea4e87057640961e6db37df0d", "name": "SDWebImage"}, {"guid": "bfdfe7dc352907fc980b868725387e9837027570fe3a09bb9c7d0d2620332306", "name": "SDWebImageWebPCoder"}], "guid": "bfdfe7dc352907fc980b868725387e982ec175b6b4d6149d1cce89f5f0b3694a", "name": "flutter_image_compress_common", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b928db338a2b0bf59a36f34e391aa676", "name": "flutter_image_compress_common.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}